{"function":"initializeCronJobs","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:21:05.179Z"}
{"function":"initializeCronJobs","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:21:05.178Z"}
{"function":"initializeCronJobs","level":"info","message":"Function Success:","params":{},"result":{"message":"All cron jobs initialized successfully"},"service":"hello-beauty-api","timestamp":"2025-07-27T12:21:05.189Z"}
{"level":"info","message":"Server running in dev mode on port 8900","service":"hello-beauty-api","timestamp":"2025-07-27 19:21:05"}
{"function":"initializeCronJobs","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:21:05.177Z"}
{"function":"initializeCronJobs","level":"info","message":"Function Success:","params":{},"result":{"message":"All cron jobs initialized successfully"},"service":"hello-beauty-api","timestamp":"2025-07-27T12:21:05.186Z"}
{"level":"info","message":"Server running in dev mode on port 8900","service":"hello-beauty-api","timestamp":"2025-07-27 19:21:05"}
{"function":"initializeCronJobs","level":"info","message":"Function Success:","params":{},"result":{"message":"All cron jobs initialized successfully"},"service":"hello-beauty-api","timestamp":"2025-07-27T12:21:05.185Z"}
{"level":"info","message":"Server running in dev mode on port 8900","service":"hello-beauty-api","timestamp":"2025-07-27 19:21:05"}
{"function":"initializeCronJobs","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:21:05.178Z"}
{"function":"initializeCronJobs","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:21:05.177Z"}
{"function":"initializeCronJobs","level":"info","message":"Function Success:","params":{},"result":{"message":"All cron jobs initialized successfully"},"service":"hello-beauty-api","timestamp":"2025-07-27T12:21:05.185Z"}
{"level":"info","message":"Server running in dev mode on port 8900","service":"hello-beauty-api","timestamp":"2025-07-27 19:21:05"}
{"function":"initializeCronJobs","level":"info","message":"Function Success:","params":{},"result":{"message":"All cron jobs initialized successfully"},"service":"hello-beauty-api","timestamp":"2025-07-27T12:21:05.185Z"}
{"level":"info","message":"Server running in dev mode on port 8900","service":"hello-beauty-api","timestamp":"2025-07-27 19:21:05"}
{"function":"initializeCronJobs","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:21:05.258Z"}
{"function":"initializeCronJobs","level":"info","message":"Function Success:","params":{},"result":{"message":"All cron jobs initialized successfully"},"service":"hello-beauty-api","timestamp":"2025-07-27T12:21:05.264Z"}
{"level":"info","message":"Server running in dev mode on port 8900","service":"hello-beauty-api","timestamp":"2025-07-27 19:21:05"}
