{"function":"initializeCronJobs","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:21:05.179Z"}
{"function":"initializeCronJobs","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:21:05.178Z"}
{"function":"initializeCronJobs","level":"info","message":"Function Success:","params":{},"result":{"message":"All cron jobs initialized successfully"},"service":"hello-beauty-api","timestamp":"2025-07-27T12:21:05.189Z"}
{"level":"info","message":"Server running in dev mode on port 8900","service":"hello-beauty-api","timestamp":"2025-07-27 19:21:05"}
{"function":"initializeCronJobs","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:21:05.177Z"}
{"function":"initializeCronJobs","level":"info","message":"Function Success:","params":{},"result":{"message":"All cron jobs initialized successfully"},"service":"hello-beauty-api","timestamp":"2025-07-27T12:21:05.186Z"}
{"level":"info","message":"Server running in dev mode on port 8900","service":"hello-beauty-api","timestamp":"2025-07-27 19:21:05"}
{"function":"initializeCronJobs","level":"info","message":"Function Success:","params":{},"result":{"message":"All cron jobs initialized successfully"},"service":"hello-beauty-api","timestamp":"2025-07-27T12:21:05.185Z"}
{"level":"info","message":"Server running in dev mode on port 8900","service":"hello-beauty-api","timestamp":"2025-07-27 19:21:05"}
{"function":"initializeCronJobs","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:21:05.178Z"}
{"function":"initializeCronJobs","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:21:05.177Z"}
{"function":"initializeCronJobs","level":"info","message":"Function Success:","params":{},"result":{"message":"All cron jobs initialized successfully"},"service":"hello-beauty-api","timestamp":"2025-07-27T12:21:05.185Z"}
{"level":"info","message":"Server running in dev mode on port 8900","service":"hello-beauty-api","timestamp":"2025-07-27 19:21:05"}
{"function":"initializeCronJobs","level":"info","message":"Function Success:","params":{},"result":{"message":"All cron jobs initialized successfully"},"service":"hello-beauty-api","timestamp":"2025-07-27T12:21:05.185Z"}
{"level":"info","message":"Server running in dev mode on port 8900","service":"hello-beauty-api","timestamp":"2025-07-27 19:21:05"}
{"function":"initializeCronJobs","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:21:05.258Z"}
{"function":"initializeCronJobs","level":"info","message":"Function Success:","params":{},"result":{"message":"All cron jobs initialized successfully"},"service":"hello-beauty-api","timestamp":"2025-07-27T12:21:05.264Z"}
{"level":"info","message":"Server running in dev mode on port 8900","service":"hello-beauty-api","timestamp":"2025-07-27 19:21:05"}
{"level":"info","message":"Shutting down server...","service":"hello-beauty-api","timestamp":"2025-07-27 19:27:22"}
{"level":"info","message":"Shutting down server...","service":"hello-beauty-api","timestamp":"2025-07-27 19:27:22"}
{"level":"info","message":"Shutting down server...","service":"hello-beauty-api","timestamp":"2025-07-27 19:27:22"}
{"function":"initializeCronJobs","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:27:26.795Z"}
{"function":"initializeCronJobs","level":"info","message":"Function Success:","params":{},"result":{"message":"All cron jobs initialized successfully"},"service":"hello-beauty-api","timestamp":"2025-07-27T12:27:26.807Z"}
{"level":"info","message":"Server running in dev mode on port 8900","service":"hello-beauty-api","timestamp":"2025-07-27 19:27:26"}
{"function":"initializeCronJobs","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:27:26.795Z"}
{"function":"initializeCronJobs","level":"info","message":"Function Success:","params":{},"result":{"message":"All cron jobs initialized successfully"},"service":"hello-beauty-api","timestamp":"2025-07-27T12:27:26.803Z"}
{"level":"info","message":"Server running in dev mode on port 8900","service":"hello-beauty-api","timestamp":"2025-07-27 19:27:26"}
{"function":"initializeCronJobs","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:27:26.841Z"}
{"function":"initializeCronJobs","level":"info","message":"Function Success:","params":{},"result":{"message":"All cron jobs initialized successfully"},"service":"hello-beauty-api","timestamp":"2025-07-27T12:27:26.871Z"}
{"level":"info","message":"Server running in dev mode on port 8900","service":"hello-beauty-api","timestamp":"2025-07-27 19:27:27"}
{"function":"initializeCronJobs","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:27:26.704Z"}
{"function":"initializeCronJobs","level":"info","message":"Function Success:","params":{},"result":{"message":"All cron jobs initialized successfully"},"service":"hello-beauty-api","timestamp":"2025-07-27T12:27:26.829Z"}
{"level":"info","message":"Server running in dev mode on port 8900","service":"hello-beauty-api","timestamp":"2025-07-27 19:27:26"}
{"function":"initializeCronJobs","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:27:26.662Z"}
{"function":"initializeCronJobs","level":"info","message":"Function Success:","params":{},"result":{"message":"All cron jobs initialized successfully"},"service":"hello-beauty-api","timestamp":"2025-07-27T12:27:26.828Z"}
{"level":"info","message":"Server running in dev mode on port 8900","service":"hello-beauty-api","timestamp":"2025-07-27 19:27:27"}
{"function":"initializeCronJobs","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:27:26.638Z"}
{"function":"initializeCronJobs","level":"info","message":"Function Success:","params":{},"result":{"message":"All cron jobs initialized successfully"},"service":"hello-beauty-api","timestamp":"2025-07-27T12:27:26.826Z"}
{"level":"info","message":"Server running in dev mode on port 8900","service":"hello-beauty-api","timestamp":"2025-07-27 19:27:27"}
{"level":"info","message":"Shutting down server...","service":"hello-beauty-api","timestamp":"2025-07-27 19:31:41"}
{"level":"info","message":"Shutting down server...","service":"hello-beauty-api","timestamp":"2025-07-27 19:31:41"}
{"level":"info","message":"Shutting down server...","service":"hello-beauty-api","timestamp":"2025-07-27 19:31:41"}
{"level":"info","message":"Shutting down server...","service":"hello-beauty-api","timestamp":"2025-07-27 19:31:41"}
{"function":"initializeCronJobs","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:31:46.378Z"}
{"function":"initializeCronJobs","level":"info","message":"Function Success:","params":{},"result":{"message":"All cron jobs initialized successfully"},"service":"hello-beauty-api","timestamp":"2025-07-27T12:31:46.388Z"}
{"level":"info","message":"Server running in dev mode on port 8900","service":"hello-beauty-api","timestamp":"2025-07-27 19:31:46"}
{"function":"initializeCronJobs","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:31:46.390Z"}
{"function":"initializeCronJobs","level":"info","message":"Function Success:","params":{},"result":{"message":"All cron jobs initialized successfully"},"service":"hello-beauty-api","timestamp":"2025-07-27T12:31:46.395Z"}
{"level":"info","message":"Server running in dev mode on port 8900","service":"hello-beauty-api","timestamp":"2025-07-27 19:31:46"}
{"function":"initializeCronJobs","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:31:46.383Z"}
{"function":"initializeCronJobs","level":"info","message":"Function Success:","params":{},"result":{"message":"All cron jobs initialized successfully"},"service":"hello-beauty-api","timestamp":"2025-07-27T12:31:46.393Z"}
{"level":"info","message":"Server running in dev mode on port 8900","service":"hello-beauty-api","timestamp":"2025-07-27 19:31:46"}
{"function":"initializeCronJobs","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:31:46.400Z"}
{"function":"initializeCronJobs","level":"info","message":"Function Success:","params":{},"result":{"message":"All cron jobs initialized successfully"},"service":"hello-beauty-api","timestamp":"2025-07-27T12:31:46.414Z"}
{"level":"info","message":"Server running in dev mode on port 8900","service":"hello-beauty-api","timestamp":"2025-07-27 19:31:46"}
{"function":"initializeCronJobs","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:31:46.401Z"}
{"function":"initializeCronJobs","level":"info","message":"Function Success:","params":{},"result":{"message":"All cron jobs initialized successfully"},"service":"hello-beauty-api","timestamp":"2025-07-27T12:31:46.414Z"}
{"level":"info","message":"Server running in dev mode on port 8900","service":"hello-beauty-api","timestamp":"2025-07-27 19:31:46"}
{"function":"initializeCronJobs","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:31:46.399Z"}
{"function":"initializeCronJobs","level":"info","message":"Function Success:","params":{},"result":{"message":"All cron jobs initialized successfully"},"service":"hello-beauty-api","timestamp":"2025-07-27T12:31:46.412Z"}
{"level":"info","message":"Server running in dev mode on port 8900","service":"hello-beauty-api","timestamp":"2025-07-27 19:31:46"}
{"level":"info","message":"Shutting down server...","service":"hello-beauty-api","timestamp":"2025-07-27 19:33:00"}
{"level":"info","message":"Shutting down server...","service":"hello-beauty-api","timestamp":"2025-07-27 19:33:00"}
{"function":"initializeCronJobs","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:33:04.309Z"}
{"function":"initializeCronJobs","level":"info","message":"Function Success:","params":{},"result":{"message":"All cron jobs initialized successfully"},"service":"hello-beauty-api","timestamp":"2025-07-27T12:33:04.318Z"}
{"level":"info","message":"Server running in dev mode on port 8900","service":"hello-beauty-api","timestamp":"2025-07-27 19:33:04"}
{"function":"initializeCronJobs","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:33:04.469Z"}
{"function":"initializeCronJobs","level":"info","message":"Function Success:","params":{},"result":{"message":"All cron jobs initialized successfully"},"service":"hello-beauty-api","timestamp":"2025-07-27T12:33:04.477Z"}
{"level":"info","message":"Server running in dev mode on port 8900","service":"hello-beauty-api","timestamp":"2025-07-27 19:33:04"}
{"function":"initializeCronJobs","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:33:04.477Z"}
{"function":"initializeCronJobs","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:33:04.483Z"}
{"function":"initializeCronJobs","level":"info","message":"Function Success:","params":{},"result":{"message":"All cron jobs initialized successfully"},"service":"hello-beauty-api","timestamp":"2025-07-27T12:33:04.483Z"}
{"level":"info","message":"Server running in dev mode on port 8900","service":"hello-beauty-api","timestamp":"2025-07-27 19:33:04"}
{"function":"initializeCronJobs","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:33:04.475Z"}
{"function":"initializeCronJobs","level":"info","message":"Function Success:","params":{},"result":{"message":"All cron jobs initialized successfully"},"service":"hello-beauty-api","timestamp":"2025-07-27T12:33:04.481Z"}
{"level":"info","message":"Server running in dev mode on port 8900","service":"hello-beauty-api","timestamp":"2025-07-27 19:33:04"}
{"function":"initializeCronJobs","level":"info","message":"Function Success:","params":{},"result":{"message":"All cron jobs initialized successfully"},"service":"hello-beauty-api","timestamp":"2025-07-27T12:33:04.488Z"}
{"level":"info","message":"Server running in dev mode on port 8900","service":"hello-beauty-api","timestamp":"2025-07-27 19:33:04"}
{"function":"initializeCronJobs","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:33:04.473Z"}
{"function":"initializeCronJobs","level":"info","message":"Function Success:","params":{},"result":{"message":"All cron jobs initialized successfully"},"service":"hello-beauty-api","timestamp":"2025-07-27T12:33:04.480Z"}
{"level":"info","message":"Server running in dev mode on port 8900","service":"hello-beauty-api","timestamp":"2025-07-27 19:33:04"}
{"level":"info","message":"Shutting down server...","service":"hello-beauty-api","timestamp":"2025-07-27 19:34:50"}
{"level":"info","message":"Shutting down server...","service":"hello-beauty-api","timestamp":"2025-07-27 19:34:50"}
{"level":"info","message":"Shutting down server...","service":"hello-beauty-api","timestamp":"2025-07-27 19:34:50"}
{"function":"initializeCronJobs","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:34:54.481Z"}
{"function":"initializeCronJobs","level":"info","message":"Function Success:","params":{},"result":{"message":"All cron jobs initialized successfully"},"service":"hello-beauty-api","timestamp":"2025-07-27T12:34:54.491Z"}
{"level":"info","message":"Server running in dev mode on port 8900","service":"hello-beauty-api","timestamp":"2025-07-27 19:34:54"}
{"function":"initializeCronJobs","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:34:54.589Z"}
{"function":"initializeCronJobs","level":"info","message":"Function Success:","params":{},"result":{"message":"All cron jobs initialized successfully"},"service":"hello-beauty-api","timestamp":"2025-07-27T12:34:54.599Z"}
{"level":"info","message":"Server running in dev mode on port 8900","service":"hello-beauty-api","timestamp":"2025-07-27 19:34:54"}
{"function":"initializeCronJobs","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:34:54.610Z"}
{"function":"initializeCronJobs","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:34:54.601Z"}
{"function":"initializeCronJobs","level":"info","message":"Function Success:","params":{},"result":{"message":"All cron jobs initialized successfully"},"service":"hello-beauty-api","timestamp":"2025-07-27T12:34:54.606Z"}
{"level":"info","message":"Server running in dev mode on port 8900","service":"hello-beauty-api","timestamp":"2025-07-27 19:34:54"}
{"function":"initializeCronJobs","level":"info","message":"Function Success:","params":{},"result":{"message":"All cron jobs initialized successfully"},"service":"hello-beauty-api","timestamp":"2025-07-27T12:34:54.621Z"}
{"level":"info","message":"Server running in dev mode on port 8900","service":"hello-beauty-api","timestamp":"2025-07-27 19:34:54"}
{"function":"initializeCronJobs","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:34:54.611Z"}
{"function":"initializeCronJobs","level":"info","message":"Function Success:","params":{},"result":{"message":"All cron jobs initialized successfully"},"service":"hello-beauty-api","timestamp":"2025-07-27T12:34:54.621Z"}
{"level":"info","message":"Server running in dev mode on port 8900","service":"hello-beauty-api","timestamp":"2025-07-27 19:34:54"}
{"function":"initializeCronJobs","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:34:54.606Z"}
{"function":"initializeCronJobs","level":"info","message":"Function Success:","params":{},"result":{"message":"All cron jobs initialized successfully"},"service":"hello-beauty-api","timestamp":"2025-07-27T12:34:54.611Z"}
{"level":"info","message":"Server running in dev mode on port 8900","service":"hello-beauty-api","timestamp":"2025-07-27 19:34:54"}
{"level":"info","message":"Shutting down server...","service":"hello-beauty-api","timestamp":"2025-07-27 19:35:46"}
{"level":"info","message":"Shutting down server...","service":"hello-beauty-api","timestamp":"2025-07-27 19:35:46"}
{"level":"info","message":"Shutting down server...","service":"hello-beauty-api","timestamp":"2025-07-27 19:35:46"}
{"level":"info","message":"Shutting down server...","service":"hello-beauty-api","timestamp":"2025-07-27 19:35:46"}
{"function":"initializeCronJobs","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:35:50.948Z"}
{"function":"initializeCronJobs","level":"info","message":"Function Success:","params":{},"result":{"message":"All cron jobs initialized successfully"},"service":"hello-beauty-api","timestamp":"2025-07-27T12:35:50.953Z"}
{"level":"info","message":"Server running in dev mode on port 8900","service":"hello-beauty-api","timestamp":"2025-07-27 19:35:50"}
{"function":"initializeCronJobs","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:35:51.010Z"}
{"function":"initializeCronJobs","level":"info","message":"Function Success:","params":{},"result":{"message":"All cron jobs initialized successfully"},"service":"hello-beauty-api","timestamp":"2025-07-27T12:35:51.015Z"}
{"level":"info","message":"Server running in dev mode on port 8900","service":"hello-beauty-api","timestamp":"2025-07-27 19:35:51"}
{"function":"initializeCronJobs","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:35:51.010Z"}
{"function":"initializeCronJobs","level":"info","message":"Function Success:","params":{},"result":{"message":"All cron jobs initialized successfully"},"service":"hello-beauty-api","timestamp":"2025-07-27T12:35:51.015Z"}
{"level":"info","message":"Server running in dev mode on port 8900","service":"hello-beauty-api","timestamp":"2025-07-27 19:35:51"}
{"function":"initializeCronJobs","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:35:51.003Z"}
{"function":"initializeCronJobs","level":"info","message":"Function Success:","params":{},"result":{"message":"All cron jobs initialized successfully"},"service":"hello-beauty-api","timestamp":"2025-07-27T12:35:51.013Z"}
{"level":"info","message":"Server running in dev mode on port 8900","service":"hello-beauty-api","timestamp":"2025-07-27 19:35:51"}
{"function":"initializeCronJobs","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:35:51.023Z"}
{"function":"initializeCronJobs","level":"info","message":"Function Success:","params":{},"result":{"message":"All cron jobs initialized successfully"},"service":"hello-beauty-api","timestamp":"2025-07-27T12:35:51.035Z"}
{"level":"info","message":"Server running in dev mode on port 8900","service":"hello-beauty-api","timestamp":"2025-07-27 19:35:51"}
{"function":"initializeCronJobs","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:35:51.026Z"}
{"function":"initializeCronJobs","level":"info","message":"Function Success:","params":{},"result":{"message":"All cron jobs initialized successfully"},"service":"hello-beauty-api","timestamp":"2025-07-27T12:35:51.047Z"}
{"level":"info","message":"Server running in dev mode on port 8900","service":"hello-beauty-api","timestamp":"2025-07-27 19:35:51"}
{"level":"info","message":"Shutting down server...","service":"hello-beauty-api","timestamp":"2025-07-27 19:36:46"}
{"level":"info","message":"Shutting down server...","service":"hello-beauty-api","timestamp":"2025-07-27 19:36:46"}
{"level":"info","message":"Shutting down server...","service":"hello-beauty-api","timestamp":"2025-07-27 19:36:46"}
{"function":"initializeCronJobs","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:36:50.408Z"}
{"function":"initializeCronJobs","level":"info","message":"Function Success:","params":{},"result":{"message":"All cron jobs initialized successfully"},"service":"hello-beauty-api","timestamp":"2025-07-27T12:36:50.418Z"}
{"level":"info","message":"Server running in dev mode on port 8900","service":"hello-beauty-api","timestamp":"2025-07-27 19:36:50"}
{"function":"initializeCronJobs","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:36:50.452Z"}
{"function":"initializeCronJobs","level":"info","message":"Function Success:","params":{},"result":{"message":"All cron jobs initialized successfully"},"service":"hello-beauty-api","timestamp":"2025-07-27T12:36:50.459Z"}
{"level":"info","message":"Server running in dev mode on port 8900","service":"hello-beauty-api","timestamp":"2025-07-27 19:36:50"}
{"function":"initializeCronJobs","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:36:50.416Z"}
{"function":"initializeCronJobs","level":"info","message":"Function Success:","params":{},"result":{"message":"All cron jobs initialized successfully"},"service":"hello-beauty-api","timestamp":"2025-07-27T12:36:50.433Z"}
{"level":"info","message":"Server running in dev mode on port 8900","service":"hello-beauty-api","timestamp":"2025-07-27 19:36:50"}
{"function":"initializeCronJobs","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:36:50.463Z"}
{"function":"initializeCronJobs","level":"info","message":"Function Success:","params":{},"result":{"message":"All cron jobs initialized successfully"},"service":"hello-beauty-api","timestamp":"2025-07-27T12:36:50.478Z"}
{"level":"info","message":"Server running in dev mode on port 8900","service":"hello-beauty-api","timestamp":"2025-07-27 19:36:50"}
{"function":"initializeCronJobs","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:36:50.502Z"}
{"function":"initializeCronJobs","level":"info","message":"Function Success:","params":{},"result":{"message":"All cron jobs initialized successfully"},"service":"hello-beauty-api","timestamp":"2025-07-27T12:36:50.519Z"}
{"level":"info","message":"Server running in dev mode on port 8900","service":"hello-beauty-api","timestamp":"2025-07-27 19:36:50"}
{"function":"initializeCronJobs","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:36:50.493Z"}
{"function":"initializeCronJobs","level":"info","message":"Function Success:","params":{},"result":{"message":"All cron jobs initialized successfully"},"service":"hello-beauty-api","timestamp":"2025-07-27T12:36:50.500Z"}
{"level":"info","message":"Server running in dev mode on port 8900","service":"hello-beauty-api","timestamp":"2025-07-27 19:36:50"}
{"function":"listPortofolio","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:37:00.607Z"}
{"function":"listPortofolio","level":"info","message":"Function Success:","params":{},"result":{"totalPortfolios":7},"service":"hello-beauty-api","timestamp":"2025-07-27T12:37:00.634Z"}
{"function":"allPortofolio","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:37:09.436Z"}
{"function":"allPortofolio","level":"info","message":"Function Success:","params":{},"result":{"totalPortfolios":7},"service":"hello-beauty-api","timestamp":"2025-07-27T12:37:09.458Z"}
{"function":"listPortofolio","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:37:31.612Z"}
{"function":"listPortofolio","level":"info","message":"Function Success:","params":{},"result":{"totalPortfolios":7},"service":"hello-beauty-api","timestamp":"2025-07-27T12:37:31.636Z"}
{"function":"listPortofolio","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:37:31.658Z"}
{"function":"listPortofolio","level":"info","message":"Function Success:","params":{},"result":{"totalPortfolios":7},"service":"hello-beauty-api","timestamp":"2025-07-27T12:37:31.684Z"}
{"function":"listPortofolio","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:37:42.953Z"}
{"function":"listPortofolio","level":"info","message":"Function Success:","params":{},"result":{"totalPortfolios":7},"service":"hello-beauty-api","timestamp":"2025-07-27T12:37:42.978Z"}
{"function":"listPortofolio","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:37:43.020Z"}
{"function":"listPortofolio","level":"info","message":"Function Success:","params":{},"result":{"totalPortfolios":7},"service":"hello-beauty-api","timestamp":"2025-07-27T12:37:43.066Z"}
{"function":"allPortofolio","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:37:44.328Z"}
{"function":"allPortofolio","level":"info","message":"Function Success:","params":{},"result":{"totalPortfolios":7},"service":"hello-beauty-api","timestamp":"2025-07-27T12:37:44.349Z"}
{"function":"listPortofolio","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:37:56.576Z"}
{"function":"listPortofolio","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:37:56.578Z"}
{"function":"listPortofolio","level":"info","message":"Function Success:","params":{},"result":{"totalPortfolios":7},"service":"hello-beauty-api","timestamp":"2025-07-27T12:37:56.604Z"}
{"function":"listPortofolio","level":"info","message":"Function Success:","params":{},"result":{"totalPortfolios":7},"service":"hello-beauty-api","timestamp":"2025-07-27T12:37:56.606Z"}
{"function":"listPortofolio","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:39:28.907Z"}
{"function":"listPortofolio","level":"info","message":"Function Success:","params":{},"result":{"totalPortfolios":7},"service":"hello-beauty-api","timestamp":"2025-07-27T12:39:28.933Z"}
{"function":"listPortofolio","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:39:56.614Z"}
{"function":"listPortofolio","level":"info","message":"Function Success:","params":{},"result":{"totalPortfolios":7},"service":"hello-beauty-api","timestamp":"2025-07-27T12:39:56.638Z"}
{"function":"listPortofolio","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:39:56.664Z"}
{"function":"listPortofolio","level":"info","message":"Function Success:","params":{},"result":{"totalPortfolios":7},"service":"hello-beauty-api","timestamp":"2025-07-27T12:39:56.687Z"}
{"function":"listPortofolio","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:41:04.374Z"}
{"function":"listPortofolio","level":"info","message":"Function Success:","params":{},"result":{"totalPortfolios":7},"service":"hello-beauty-api","timestamp":"2025-07-27T12:41:04.407Z"}
{"function":"listPortofolio","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:41:04.544Z"}
{"function":"listPortofolio","level":"info","message":"Function Success:","params":{},"result":{"totalPortfolios":7},"service":"hello-beauty-api","timestamp":"2025-07-27T12:41:04.579Z"}
{"function":"allPortofolio","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T12:41:14.767Z"}
{"function":"allPortofolio","level":"info","message":"Function Success:","params":{},"result":{"totalPortfolios":7},"service":"hello-beauty-api","timestamp":"2025-07-27T12:41:14.792Z"}
{"function":"listPortofolio","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T13:06:52.010Z"}
{"function":"listPortofolio","level":"info","message":"Function Success:","params":{},"result":{"totalPortfolios":7},"service":"hello-beauty-api","timestamp":"2025-07-27T13:06:52.046Z"}
{"function":"listPortofolio","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T13:06:52.097Z"}
{"function":"listPortofolio","level":"info","message":"Function Success:","params":{},"result":{"totalPortfolios":7},"service":"hello-beauty-api","timestamp":"2025-07-27T13:06:52.141Z"}
{"function":"allPortofolio","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T13:06:59.855Z"}
{"function":"allPortofolio","level":"info","message":"Function Success:","params":{},"result":{"totalPortfolios":7},"service":"hello-beauty-api","timestamp":"2025-07-27T13:06:59.886Z"}
{"function":"listPortofolio","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T13:15:36.032Z"}
{"function":"listPortofolio","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T13:15:36.034Z"}
{"function":"listPortofolio","level":"info","message":"Function Success:","params":{},"result":{"totalPortfolios":7},"service":"hello-beauty-api","timestamp":"2025-07-27T13:15:36.058Z"}
{"function":"listPortofolio","level":"info","message":"Function Success:","params":{},"result":{"totalPortfolios":7},"service":"hello-beauty-api","timestamp":"2025-07-27T13:15:36.081Z"}
{"function":"listPortofolio","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T13:16:01.491Z"}
{"function":"listPortofolio","level":"info","message":"Function Call:","params":{},"service":"hello-beauty-api","timestamp":"2025-07-27T13:16:01.496Z"}
{"function":"listPortofolio","level":"info","message":"Function Success:","params":{},"result":{"totalPortfolios":7},"service":"hello-beauty-api","timestamp":"2025-07-27T13:16:01.516Z"}
{"function":"listPortofolio","level":"info","message":"Function Success:","params":{},"result":{"totalPortfolios":7},"service":"hello-beauty-api","timestamp":"2025-07-27T13:16:01.519Z"}
