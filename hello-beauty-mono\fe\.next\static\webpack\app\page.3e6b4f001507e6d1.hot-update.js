"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/_helper/api.js":
/*!****************************!*\
  !*** ./app/_helper/api.js ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   api: function() { return /* binding */ api; },\n/* harmony export */   apiPublic: function() { return /* binding */ apiPublic; },\n/* harmony export */   apiUpload: function() { return /* binding */ apiUpload; }\n/* harmony export */ });\nconst baseUrl = \"http//:localhost:8900/v1\";\nconst getAuth = ()=>{\n    const name = \"hb_token\";\n    const value = \"; \".concat(document.cookie);\n    const parts = value.split(\"; \".concat(name, \"=\"));\n    if (parts.length === 2) return parts.pop().split(\";\").shift();\n};\nconst api = async (method, url, data)=>{\n    const token = await getAuth();\n    const options = {\n        method: method,\n        headers: {\n            \"Content-Type\": \"application/json\",\n            \"Authorization\": \"Bearer \".concat(token)\n        }\n    };\n    // Only add body for non-GET requests\n    if (method !== \"GET\" && data) {\n        options.body = JSON.stringify(data);\n    }\n    const response = await fetch(baseUrl + url, options);\n    if (!response.ok) {\n        // Check if response is JSON before trying to parse\n        const contentType = response.headers.get(\"content-type\");\n        if (contentType && contentType.includes(\"application/json\")) {\n            const error = await response.json();\n            throw error;\n        } else {\n            // If not JSON, create a generic error\n            throw new Error(\"HTTP \".concat(response.status, \": \").concat(response.statusText));\n        }\n    }\n    return response.json();\n};\n// API for public endpoints (no authentication required)\nconst apiPublic = async (method, url, data)=>{\n    const options = {\n        method: method,\n        headers: {\n            \"Content-Type\": \"application/json\"\n        }\n    };\n    // Only add body for non-GET requests\n    if (method !== \"GET\" && data) {\n        options.body = JSON.stringify(data);\n    }\n    const response = await fetch(baseUrl + url, options);\n    if (!response.ok) {\n        // Check if response is JSON before trying to parse\n        const contentType = response.headers.get(\"content-type\");\n        if (contentType && contentType.includes(\"application/json\")) {\n            const error = await response.json();\n            throw error;\n        } else {\n            // If not JSON, create a generic error\n            throw new Error(\"HTTP \".concat(response.status, \": \").concat(response.statusText));\n        }\n    }\n    return response.json();\n};\nconst apiUpload = async (url, data)=>{\n    const token = await getAuth();\n    const response = await fetch(baseUrl + url, {\n        method: \"POST\",\n        headers: {\n            Authorization: \"Bearer \".concat(token)\n        },\n        body: data\n    });\n    // intercept response here\n    if (response.status === 401) {\n        // redirect to login\n        window.location.href = \"/login\";\n    }\n    if (!response.ok) {\n        // Check if response is JSON before trying to parse\n        const contentType = response.headers.get(\"content-type\");\n        if (contentType && contentType.includes(\"application/json\")) {\n            const error = await response.json();\n            throw error;\n        } else {\n            // If not JSON, create a generic error\n            throw new Error(\"HTTP \".concat(response.status, \": \").concat(response.statusText));\n        }\n    }\n    return response.json();\n};\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/_helper/api.js\n"));

/***/ })

});