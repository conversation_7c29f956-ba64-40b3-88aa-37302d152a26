// Import Swiper React components
import {Swiper, SwiperSlide} from 'swiper/react';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/pagination';

// import './styles.css';
// import required modules
import {Autoplay, Pagination} from 'swiper/modules';
// import Image from 'next/image';
import { useEffect, useState } from 'react';
import { api } from '@/app/_helper/api';
import { Icon } from '@iconify/react';

const Gallery = () => {
  const [gallery, setGallery] = useState([]);
  const [allGallery, setAllGallery] = useState([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);
  const [loading, setLoading] = useState(false);

  const getGallery = async () => {
    try {
      const response = await api("GET", "/portofolio");
      console.log("Gallery response:", response.data);
      setGallery(response.data);
    } catch (error) {
      console.error("Error fetching gallery:", error);
    }
  }

  const getAllGallery = async () => {
    try {
      setLoading(true);
      const response = await api("GET", "/portofolio/all");
      console.log("All gallery response:", response.data);
      setAllGallery(response.data);
      setLoading(false);
    } catch (error) {
      setLoading(false);
      console.error("Error fetching all gallery:", error);
    }
  }

  useEffect(() => {
    getGallery()
  }, [])

  const openModal = () => {
    setIsModalOpen(true);
    getAllGallery();
  }

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedImage(null);
  }

  const openImageModal = (imageUrl) => {
    setSelectedImage(imageUrl);
  }

  const closeImageModal = () => {
    setSelectedImage(null);
  }

  return (
    <>
      <div className="relative">
        <Swiper slidesPerView={2} autoplay={{delay: 2500,disableOnInteraction: false,}} centeredSlides={true} spaceBetween={8} pagination={{clickable: true,}} modules={[Pagination, Autoplay]}>
          {gallery.map((item, index) => (
            <SwiperSlide key={index}>
              <img
                src={item.fullUrl}
                width="400"
                height="320"
                className="h-[320px] rounded-lg object-cover"
                alt={`Gallery ${index + 1}`}
                onError={(e) => {
                  console.error("Image failed to load:", item.fullUrl);
                  e.target.style.display = 'none';
                }}
                onLoad={() => {
                  console.log("Image loaded successfully:", item.fullUrl);
                }}
              />
            </SwiperSlide>
          ))}
        </Swiper>

        {/* See All Button */}
        <div className="flex justify-center mt-4">
          <button
            onClick={openModal}
            className="bg-hb-pink text-white px-6 py-2 rounded-full text-sm font-medium flex items-center gap-2 hover:bg-hb-pink/90 transition-colors"
          >
            <span>See All Portfolio</span>
            <Icon icon="mdi:arrow-right" className="text-lg" />
          </button>
        </div>
      </div>

      {/* Modal for All Portfolio */}
      {isModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-[100000] p-4">
          <div className="bg-white rounded-lg max-w-6xl w-full max-h-[90vh] overflow-hidden">
            {/* Modal Header */}
            <div className="flex justify-between items-center p-4 border-b">
              <h2 className="text-xl font-semibold text-hb-pink">All Portfolio</h2>
              <button
                onClick={closeModal}
                className="text-gray-500 hover:text-gray-700 text-2xl w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100"
              >
                &times;
              </button>
            </div>

            {/* Modal Content */}
            <div className="p-4 overflow-y-auto max-h-[calc(90vh-120px)]">
              {loading && (
                <div className="flex items-center justify-center py-12">
                  <Icon
                    icon="svg-spinners:180-ring-with-bg"
                    className="text-hb-pink animate-spin text-3xl"
                  />
                </div>
              )}

              {!loading && (
                <div className="columns-2 sm:columns-3 md:columns-4 lg:columns-5 gap-4">
                  {allGallery.map((item, index) => (
                    <div key={index} className="break-inside-avoid mb-4">
                      <img
                        src={item.fullUrl}
                        className="w-full rounded-lg object-cover cursor-pointer hover:opacity-90 transition-opacity shadow-sm hover:shadow-md"
                        alt={`Portfolio ${index + 1}`}
                        onClick={() => openImageModal(item.fullUrl)}
                      />
                      {item.packageName && (
                        <p className="text-xs text-gray-600 mt-1 text-center bg-gray-50 rounded px-2 py-1">{item.packageName}</p>
                      )}
                    </div>
                  ))}
                </div>
              )}

              {!loading && allGallery.length === 0 && (
                <div className="text-center py-12 text-gray-500">
                  <Icon icon="mdi:image-off-outline" className="text-6xl mb-4 mx-auto" />
                  <p>No portfolio found</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Image Preview Modal */}
      {selectedImage && (
        <div
          className="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-[100001]"
          onClick={closeImageModal}
        >
          <div className="relative max-w-4xl max-h-[90vh] p-4">
            <img
              src={selectedImage}
              alt="Selected Portfolio"
              className="max-w-full max-h-full object-contain rounded-lg"
            />
            <button
              className="absolute top-2 right-2 text-white text-3xl hover:text-gray-300"
              onClick={closeImageModal}
            >
              &times;
            </button>
          </div>
        </div>
      )}
    </>
  );
}

export default Gallery;
