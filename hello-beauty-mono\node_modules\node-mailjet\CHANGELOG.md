# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

### [6.0.9](https://github.com/mailjet/mailjet-apiv3-nodejs/compare/v6.0.8...v6.0.9) (2025-07-25)


### Bug Fixes

* update dependencies ([#295](https://github.com/mailjet/mailjet-apiv3-nodejs/issues/295)) ([4fdec4e](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/4fdec4ec1c54b423d3bf975d30a1faad68ce3dfa))
* update template type ([#294](https://github.com/mailjet/mailjet-apiv3-nodejs/issues/294)) ([957d51c](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/957d51cb47bc4fd67a4aed98ef40d0871fcefba6))

### [6.0.8](https://github.com/mailjet/mailjet-apiv3-nodejs/compare/v6.0.7...v6.0.8) (2025-03-10)


### Bug Fixes

* Add validation for managecontact action payload ([#290](https://github.com/mailjet/mailjet-apiv3-nodejs/issues/290)) ([89ec4b2](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/89ec4b2e8f89ddb6672c9fa8e692951416b24009))


### Other changes

* update version type to be more precise than string ([#259](https://github.com/mailjet/mailjet-apiv3-nodejs/issues/259)) ([124de7b](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/124de7bbdcd98e080529b46489ecf6818d0ef1f6))

### [6.0.7](https://github.com/mailjet/mailjet-apiv3-nodejs/compare/v6.0.6...v6.0.7) (2025-03-05)


### Other changes

* fix vulnerable dev deps ([43bbe26](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/43bbe266184717e5719adfb2c36c65e48df1fe40))

### [6.0.6](https://github.com/mailjet/mailjet-apiv3-nodejs/compare/v6.0.5...v6.0.6) (2024-09-02)


### Bug Fixes

* upgrade axios version to fix CVE-2023-26159 ([6342638](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/6342638c9dd6fcd8e62a071b60fe8aefc7d7ca5c))

### [6.0.5](https://github.com/mailjet/mailjet-apiv3-nodejs/compare/v6.0.4...v6.0.5) (2023-11-27)


### Bug Fixes

* typescript returned type, use specific axios version ([c6329ea](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/c6329ea01c2fa0fc05f9838c8279d0b48a119cbb))

### [6.0.4](https://github.com/mailjet/mailjet-apiv3-nodejs/compare/v6.0.3...v6.0.4) (2023-07-24)


### Bug Fixes

* content-length header for GET requests ([be51c9d](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/be51c9d94e20b35329a3331ddfe5ab80030fbf7c))

### [6.0.3](https://github.com/mailjet/mailjet-apiv3-nodejs/compare/v6.0.2...v6.0.3) (2023-07-12)


### Build changes

* **deps-dev:** bump webpack from 5.72.1 to 5.76.0 ([9b66c92](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/9b66c92138673f668ad7bcfac9d97c571e9cc10f))
* **deps:** bump jose in /examples/firebase/functions ([4854e8d](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/4854e8d6dc183fd31218d5fd86a9eab829ef0773))
* **deps:** bump json5 from 1.0.1 to 1.0.2 ([366f3c8](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/366f3c8326a5ae9411f38b8a5561503f5ca3373e))
* **deps:** bump jsonwebtoken and firebase-admin ([6d58037](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/6d58037cea542b597b5cb9e363ab8e5d145f5a08))
* **deps:** bump loader-utils from 2.0.2 to 2.0.4 ([d72014e](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/d72014e386c8588c7949135dfe5146ae26580e13))

### [6.0.2](https://github.com/mailjet/mailjet-apiv3-nodejs/compare/v6.0.1...v6.0.2) (2023-01-31)


### Bug Fixes

* named export missing parameter ([2f7b040](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/2f7b040097834f7b24c93bd933ef82fe9da5c6ed))


### Docs changes

* update Readme ([9a3471d](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/9a3471d8bdd246e16b3fcc18957bf7411502e00c))


### Other changes

* add new built files ([882349a](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/882349ad9b2e65d9323c4426e0e38bb2b73f6f45))

### [6.0.1](https://github.com/mailjet/mailjet-apiv3-nodejs/compare/v6.0.0...v6.0.1) (2023-01-09)


### Bug Fixes

* **types:** Fix TypeScript imports ([02fe5e9](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/02fe5e945d9cc9fb1361b9bc681be097f40fddd6))


### Docs changes

* Update TypeScript examples in Readme ([9c202d0](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/9c202d02de6c3af4271adb5e694bef7c6ab5b7de))


### Other changes

* Update TypeScript declaration for SendEmail Body type ([42f49d1](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/42f49d1baf8b2a9227e9b3d54141d9a50962c6c1))

## [6.0.0](https://github.com/mailjet/mailjet-apiv3-nodejs/compare/v5.1.1...v6.0.0) (2022-12-27)


### Bug Fixes

* **types:** Build Typescript changes ([f683f70](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/f683f708db0245f74568f1c4bf0e06cc5f0a86c7))
* **types:** Typescript exported types ([0e19bab](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/0e19bab228ffc83d19b17c270e0156f58f60cfe1))


### Docs changes

* Update Readme ([29bb64f](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/29bb64f23092d72728c19208ac2367293433d62b))


### Breaking changes

* Update TypeScript namings in the code and tests ([904735d](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/904735db615480de2d1614f1b93a27a3de37d73d))

### [5.1.1](https://github.com/mailjet/mailjet-apiv3-nodejs/compare/v5.1.0...v5.1.1) (2022-07-27)


### Other changes

* Add application example with using Firebase Functions and Mailjet lib ([690ff1e](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/690ff1e0a609c0e31b13f3b79c5b0ddaf0a934ad))


### Docs changes

* Add mention about Firebase application example to README ([0154a0a](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/0154a0ae0de63a3d4ebdd5e1c3a38be0408c6cb9))

## [5.1.0](https://github.com/mailjet/mailjet-apiv3-nodejs/compare/v5.0.1...v5.1.0) (2022-07-22)


### Added features

* Add full TypeScript cover for Mailjet types ([784c4cd](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/784c4cd79c5531aaeacbdde5547e2793b57d1427))


### Other changes

* Change global and TypeScript rules ([fa4fb60](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/fa4fb606f4e380886734468ac18c2c90e05ebd17))


### Docs changes

* Update TypeScript documentation part; Add example of using Mailjet types ([15f2d11](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/15f2d11dae63fe54fdb350864b99129fcc26afb0))


### Dependency changes for security

* Change webpack dependency terser package 5.0.0 - 5.14.1 ([959018a](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/959018a77ff36a70769f36e22f57bdc89cba2157))

### [5.0.1](https://github.com/mailjet/mailjet-apiv3-nodejs/compare/v5.0.0...v5.0.1) (2022-06-30)


### Build changes

* Add standard-version updater for README ([e615ff7](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/e615ff7cf7a07f224a4f542422b1c3bbd20179f7))
* Add VersionBump script ([f2ed676](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/f2ed676134cd1dcf091fe766b7c76b2c12e625a4))
* Move standard-version config from package.json to .versionrc.js ([122e4a5](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/122e4a5c7975773e891591cf4f3431a0e7cd77e1))

## [5.0.0](https://github.com/mailjet/mailjet-apiv3-nodejs/compare/v4.0.1...v5.0.0) (2022-06-24)


### Bug Fixes

* PreparePackage script ([57730b8](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/57730b8ad1a6de8a0de51c4fae6c31d654ebea34))


### Breaking changes

* Replace superagent http client with axios ([54c06d0](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/54c06d05b715e0d3493b03973156603e64f1e3d9))


### Added features

* Update "request" method signature in the class Request ([bbed64a](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/bbed64aea20b5bf7326d301bedc743bc6d6b14d2))


### Tests

* Update integration tests ([39d4cb5](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/39d4cb5544560d77079381f5e1ccba00f9ad4d9e))
* Update unit and integration tests after change Request.request method ([5362aa5](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/5362aa532601d0f0512f175cb68e7e1dd5c53af9))
* Update unit tests for Client and Request ([87c456e](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/87c456e27d728396b98d1a5cb8d6fceac719c459))


### Docs changes

* Update "sendmail" example ([0393327](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/03933278088ef1cad840e8b2d4a561fda9f5b7f2))
* Update auto-generated TypeScript docs ([758d080](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/758d0809296e63fcee2d1ec6c79e61debba812c2))
* Update auto-generated TypeScript docs after change Request.request method ([d0ee581](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/d0ee581fbdc172694feadb208a372785754632db))
* Update example for browser ([024484b](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/024484bd593ca12814aede3d22a8c1e012e3e385))
* Update example for NodeJS ([34a449a](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/34a449adc1c2adb609d032fe3f83a2a933f545a5))
* Update example for ReactJS ([bc120ba](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/bc120bac988e2ec2a0c7843500dbf2b43ea00e30))
* Update examples after change Request.request method ([578a59c](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/578a59c8fb99d9801339939bd79ccabcefa64e4a))
* Update README ([6eb525d](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/6eb525dfdea6c4af53af7980efc46838efc3d6ad))
* Update README after change Request.request method ([35df465](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/35df465a9602c8c072ca1fee8db4dc7415921b3f))


### Build changes

* Move PreparePackage script to scripts folder ([41b07f1](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/41b07f100dea92c62bb6461ff817a1da679e9639))
* Update library bundle ([5d40aa7](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/5d40aa74c8046cfd44098fcd304e1fd9303f903e))
* Update library bundle after change Request.request method ([34e85e0](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/34e85e0a757b34eea71612c1492871ac9a7fd6bb))

### [4.0.1](https://github.com/mailjet/mailjet-apiv3-nodejs/compare/v4.0.0...v4.0.1) (2022-06-14)


### Bug Fixes

* Typo in readme ([80a2902](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/80a2902fd9184c002821d53d9fc8c63b1e9f4468))

## [4.0.0](https://github.com/mailjet/mailjet-apiv3-nodejs/compare/3.4.1...v4.0.0) (2022-06-10)


### Bug Fixes

* Package init script ([92ab6e2](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/92ab6e29de41ddaaae03d7480f211d7e3f651f3c))
* Prepare package script ([e4cea3a](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/e4cea3af61d3170f595d4946e0e05a0c5150da01))

### Other changes

* Add commitlint ([8ef0b9e](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/8ef0b9eca2eff8e5669612d2bd48d436dfbb4339))
* Add husky ([a707dee](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/a707dee5fc601e967ee9097e739e9ec90b32de5b))
* Add init and link scripts to package scripts ([52a149d](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/52a149dd841919816b59cd175e83ae3e95ff6a7d))
* Add standard-version to auto-generate changelog file ([487e580](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/487e5803775679ce15628811b12dc6fa2bf31a4b))


### Added features

* Add public methods for accessing private properties in Client ([1410adc](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/1410adc61cb096ce4b4d5559a298ca7cb21a02ce))
* Add TypeScript class access modificators in Request ([f7c8974](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/f7c897493897a84cfe2b7e37698e49077a944345))


### Tests

* Update Unit and Integration test after updated Client ([b9783f2](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/b9783f2714f62b63a71aac95790ce2be7c85dc6c))


### Docs changes

* Update auto-generated TypeScript docs ([612145f](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/612145fcfefe569bed2185909a2230052d59c704))
* Update main README; Update README for browser-side examples ([f39112f](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/f39112f896427f28a2b28a091d7eb6e5c78e8f54))
* Update README ([41aae6a](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/41aae6afeb2034ab6ea596005e76633ef1decce3))
* Update README for node example ([efa90ff](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/efa90ff73734fef042db1882e5664b4771520555))
* Update README for react example ([c98e61c](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/c98e61c6d96c6bd8115935cf48b62130bd27543c))


### Build changes

* Update dist with applied changes from Client and changed TS types ([eb4dfdf](https://github.com/mailjet/mailjet-apiv3-nodejs/commits/eb4dfdf1cff30546b7f5acfee449a745dbdd38da))
