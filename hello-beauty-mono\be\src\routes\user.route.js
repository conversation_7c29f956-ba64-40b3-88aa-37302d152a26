const express = require('express');
const { registerUser, loginUser, sendEmailForgotPassword, validateTokenResetPassword } = require('../controllers/user/authController');
const { listPackage, portofolioPackage, listPortofolio, allPortofolio } = require('../controllers/user/packageController');
const { listLocation } = require('../controllers/user/locationController');
const { createOrder, detailOrder, listOrder } = require('../controllers/user/orderController');
const { detailPay, createPay, detailPayList } = require('../controllers/user/payController');
const { getProfile, updatePhoto, updateProfile, changePassword } = require('../controllers/user/profilController');
const checkSession = require('../middlewares/check-auth');
const { listPaymentMethod } = require('../controllers/user/paymentMethodController');
const { historyReferral } = require('../controllers/user/referralController');
const { getDpSetting } = require('../controllers/user/settingController');
const { addReview } = require('../controllers/user/reviewController');
const { checkVoucher } = require('../controllers/user/voucherController');
const router = express.Router();

// auth
const authRouter = express.Router();
authRouter.post('/login', loginUser)
authRouter.post('/register', registerUser)
authRouter.post('/forgot-password', sendEmailForgotPassword)
authRouter.post('/validate-reset-password', validateTokenResetPassword)
// lokasi
const locationRouter = express.Router();
locationRouter.get('/', listLocation)

// payment
const paymentMethodRouter = express.Router();
paymentMethodRouter.get('/', listPaymentMethod)

// paket
const packageRouter = express.Router();
packageRouter.get('/', listPackage)
packageRouter.get('/:id', portofolioPackage)

// order
const orderRouter = express.Router();
orderRouter.post('/', createOrder)
orderRouter.get('/:id', detailOrder)
orderRouter.get('/', listOrder)

const payRouter = express.Router();
payRouter.get('/:id', detailPay)
payRouter.get('/list/:id', detailPayList)
payRouter.post('/create/:id', createPay)

// me
const meRouter = express.Router();
meRouter.get('/', getProfile)
meRouter.put('/', updateProfile)
meRouter.post('/update-photo', updatePhoto)
meRouter.post('/change-password', changePassword)

const referralRouter = express.Router();
referralRouter.get('/history', historyReferral)

const settingRouter = express.Router();
settingRouter.get('/dp', getDpSetting)

const reviewRouter = express.Router();
reviewRouter.post('/', addReview)

const portofolioRouter = express.Router();
portofolioRouter.get('/', listPortofolio)
portofolioRouter.get('/all', allPortofolio)

// Voucher routes
router.get("/voucher/check", checkVoucher);

// Direct portfolio routes for testing
router.get("/portofolio-all", allPortofolio);

// assign router
router.use('/auth', authRouter);
router.use('/package', packageRouter);
router.use('/location', locationRouter);
router.use('/payment-method', paymentMethodRouter);
router.use('/order', orderRouter);
router.use('/pay', payRouter);
router.use('/me', checkSession, meRouter);
router.use('/referral', checkSession, referralRouter);
router.use('/review', checkSession, reviewRouter);
router.use('/setting', settingRouter);
router.use('/portofolio', portofolioRouter);

module.exports = router;
