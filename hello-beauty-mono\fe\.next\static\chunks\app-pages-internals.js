/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app-pages-internals"],{

/***/ "(app-pages-browser)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CHelloBeauty-New%5C%5Chello-beauty-mono%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CHelloBeauty-New%5C%5Chello-beauty-mono%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CHelloBeauty-New%5C%5Chello-beauty-mono%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CHelloBeauty-New%5C%5Chello-beauty-mono%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CHelloBeauty-New%5C%5Chello-beauty-mono%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CHelloBeauty-New%5C%5Chello-beauty-mono%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CHelloBeauty-New%5C%5Chello-beauty-mono%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CHelloBeauty-New%5C%5Chello-beauty-mono%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CHelloBeauty-New%5C%5Chello-beauty-mono%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CHelloBeauty-New%5C%5Chello-beauty-mono%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CHelloBeauty-New%5C%5Chello-beauty-mono%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CHelloBeauty-New%5C%5Chello-beauty-mono%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/app-router.js */ \"(app-pages-browser)/../node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/client-page.js */ \"(app-pages-browser)/../node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/error-boundary.js */ \"(app-pages-browser)/../node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/layout-router.js */ \"(app-pages-browser)/../node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/not-found-boundary.js */ \"(app-pages-browser)/../node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/render-from-template-context.js */ \"(app-pages-browser)/../node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CHelloBeauty-New%5C%5Chello-beauty-mono%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CHelloBeauty-New%5C%5Chello-beauty-mono%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CHelloBeauty-New%5C%5Chello-beauty-mono%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CHelloBeauty-New%5C%5Chello-beauty-mono%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CHelloBeauty-New%5C%5Chello-beauty-mono%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CHelloBeauty-New%5C%5Chello-beauty-mono%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/next/dist/lib/url.js":
/*!********************************************!*\
  !*** ../node_modules/next/dist/lib/url.js ***!
  \********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    getPathname: function() {\n        return getPathname;\n    },\n    isFullStringUrl: function() {\n        return isFullStringUrl;\n    }\n});\nconst DUMMY_ORIGIN = \"http://n\";\nfunction getUrlWithoutHost(url) {\n    return new URL(url, DUMMY_ORIGIN);\n}\nfunction getPathname(url) {\n    return getUrlWithoutHost(url).pathname;\n}\nfunction isFullStringUrl(url) {\n    return /https?:\\/\\//.test(url);\n}\n\n//# sourceMappingURL=url.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2xpYi91cmwuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkM7QUFDN0M7QUFDQSxDQUFDLEVBQUM7QUFDRixNQUFNLENBR0w7QUFDRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvbGliL3VybC5qcz8wZTM0Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7XG4gICAgdmFsdWU6IHRydWVcbn0pO1xuMCAmJiAobW9kdWxlLmV4cG9ydHMgPSB7XG4gICAgZ2V0UGF0aG5hbWU6IG51bGwsXG4gICAgaXNGdWxsU3RyaW5nVXJsOiBudWxsXG59KTtcbmZ1bmN0aW9uIF9leHBvcnQodGFyZ2V0LCBhbGwpIHtcbiAgICBmb3IodmFyIG5hbWUgaW4gYWxsKU9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0YXJnZXQsIG5hbWUsIHtcbiAgICAgICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICAgICAgZ2V0OiBhbGxbbmFtZV1cbiAgICB9KTtcbn1cbl9leHBvcnQoZXhwb3J0cywge1xuICAgIGdldFBhdGhuYW1lOiBmdW5jdGlvbigpIHtcbiAgICAgICAgcmV0dXJuIGdldFBhdGhuYW1lO1xuICAgIH0sXG4gICAgaXNGdWxsU3RyaW5nVXJsOiBmdW5jdGlvbigpIHtcbiAgICAgICAgcmV0dXJuIGlzRnVsbFN0cmluZ1VybDtcbiAgICB9XG59KTtcbmNvbnN0IERVTU1ZX09SSUdJTiA9IFwiaHR0cDovL25cIjtcbmZ1bmN0aW9uIGdldFVybFdpdGhvdXRIb3N0KHVybCkge1xuICAgIHJldHVybiBuZXcgVVJMKHVybCwgRFVNTVlfT1JJR0lOKTtcbn1cbmZ1bmN0aW9uIGdldFBhdGhuYW1lKHVybCkge1xuICAgIHJldHVybiBnZXRVcmxXaXRob3V0SG9zdCh1cmwpLnBhdGhuYW1lO1xufVxuZnVuY3Rpb24gaXNGdWxsU3RyaW5nVXJsKHVybCkge1xuICAgIHJldHVybiAvaHR0cHM/OlxcL1xcLy8udGVzdCh1cmwpO1xufVxuXG4vLyMgc291cmNlTWFwcGluZ1VSTD11cmwuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/next/dist/lib/url.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/next/dist/server/app-render/dynamic-rendering.js":
/*!************************************************************************!*\
  !*** ../node_modules/next/dist/server/app-render/dynamic-rendering.js ***!
  \************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/**\n * The functions provided by this module are used to communicate certain properties\n * about the currently running code so that Next.js can make decisions on how to handle\n * the current execution in different rendering modes such as pre-rendering, resuming, and SSR.\n *\n * Today Next.js treats all code as potentially static. Certain APIs may only make sense when dynamically rendering.\n * Traditionally this meant deopting the entire render to dynamic however with PPR we can now deopt parts\n * of a React tree as dynamic while still keeping other parts static. There are really two different kinds of\n * Dynamic indications.\n *\n * The first is simply an intention to be dynamic. unstable_noStore is an example of this where\n * the currently executing code simply declares that the current scope is dynamic but if you use it\n * inside unstable_cache it can still be cached. This type of indication can be removed if we ever\n * make the default dynamic to begin with because the only way you would ever be static is inside\n * a cache scope which this indication does not affect.\n *\n * The second is an indication that a dynamic data source was read. This is a stronger form of dynamic\n * because it means that it is inappropriate to cache this at all. using a dynamic data source inside\n * unstable_cache should error. If you want to use some dynamic data inside unstable_cache you should\n * read that data outside the cache and pass it in as an argument to the cached function.\n */ // Once postpone is in stable we should switch to importing the postpone export directly\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    Postpone: function() {\n        return Postpone;\n    },\n    createPostponedAbortSignal: function() {\n        return createPostponedAbortSignal;\n    },\n    createPrerenderState: function() {\n        return createPrerenderState;\n    },\n    formatDynamicAPIAccesses: function() {\n        return formatDynamicAPIAccesses;\n    },\n    markCurrentScopeAsDynamic: function() {\n        return markCurrentScopeAsDynamic;\n    },\n    trackDynamicDataAccessed: function() {\n        return trackDynamicDataAccessed;\n    },\n    trackDynamicFetch: function() {\n        return trackDynamicFetch;\n    },\n    usedDynamicAPIs: function() {\n        return usedDynamicAPIs;\n    }\n});\nconst _react = /*#__PURE__*/ _interop_require_default(__webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/index.js\"));\nconst _hooksservercontext = __webpack_require__(/*! ../../client/components/hooks-server-context */ \"(app-pages-browser)/../node_modules/next/dist/client/components/hooks-server-context.js\");\nconst _staticgenerationbailout = __webpack_require__(/*! ../../client/components/static-generation-bailout */ \"(app-pages-browser)/../node_modules/next/dist/client/components/static-generation-bailout.js\");\nconst _url = __webpack_require__(/*! ../../lib/url */ \"(app-pages-browser)/../node_modules/next/dist/lib/url.js\");\nfunction _interop_require_default(obj) {\n    return obj && obj.__esModule ? obj : {\n        default: obj\n    };\n}\nconst hasPostpone = typeof _react.default.unstable_postpone === \"function\";\nfunction createPrerenderState(isDebugSkeleton) {\n    return {\n        isDebugSkeleton,\n        dynamicAccesses: []\n    };\n}\nfunction markCurrentScopeAsDynamic(store, expression) {\n    const pathname = (0, _url.getPathname)(store.urlPathname);\n    if (store.isUnstableCacheCallback) {\n        // inside cache scopes marking a scope as dynamic has no effect because the outer cache scope\n        // creates a cache boundary. This is subtly different from reading a dynamic data source which is\n        // forbidden inside a cache scope.\n        return;\n    } else if (store.dynamicShouldError) {\n        throw new _staticgenerationbailout.StaticGenBailoutError(`Route ${pathname} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`);\n    } else if (// We are in a prerender (PPR enabled, during build)\n    store.prerenderState) {\n        // We track that we had a dynamic scope that postponed.\n        // This will be used by the renderer to decide whether\n        // the prerender requires a resume\n        postponeWithTracking(store.prerenderState, expression, pathname);\n    } else {\n        store.revalidate = 0;\n        if (store.isStaticGeneration) {\n            // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n            const err = new _hooksservercontext.DynamicServerError(`Route ${pathname} couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`);\n            store.dynamicUsageDescription = expression;\n            store.dynamicUsageStack = err.stack;\n            throw err;\n        }\n    }\n}\nfunction trackDynamicDataAccessed(store, expression) {\n    const pathname = (0, _url.getPathname)(store.urlPathname);\n    if (store.isUnstableCacheCallback) {\n        throw new Error(`Route ${pathname} used \"${expression}\" inside a function cached with \"unstable_cache(...)\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"${expression}\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`);\n    } else if (store.dynamicShouldError) {\n        throw new _staticgenerationbailout.StaticGenBailoutError(`Route ${pathname} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`);\n    } else if (// We are in a prerender (PPR enabled, during build)\n    store.prerenderState) {\n        // We track that we had a dynamic scope that postponed.\n        // This will be used by the renderer to decide whether\n        // the prerender requires a resume\n        postponeWithTracking(store.prerenderState, expression, pathname);\n    } else {\n        store.revalidate = 0;\n        if (store.isStaticGeneration) {\n            // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n            const err = new _hooksservercontext.DynamicServerError(`Route ${pathname} couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`);\n            store.dynamicUsageDescription = expression;\n            store.dynamicUsageStack = err.stack;\n            throw err;\n        }\n    }\n}\nfunction Postpone({ reason, prerenderState, pathname }) {\n    postponeWithTracking(prerenderState, reason, pathname);\n}\nfunction trackDynamicFetch(store, expression) {\n    if (store.prerenderState) {\n        postponeWithTracking(store.prerenderState, expression, store.urlPathname);\n    }\n}\nfunction postponeWithTracking(prerenderState, expression, pathname) {\n    assertPostpone();\n    const reason = `Route ${pathname} needs to bail out of prerendering at this point because it used ${expression}. ` + `React throws this special object to indicate where. It should not be caught by ` + `your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`;\n    prerenderState.dynamicAccesses.push({\n        // When we aren't debugging, we don't need to create another error for the\n        // stack trace.\n        stack: prerenderState.isDebugSkeleton ? new Error().stack : undefined,\n        expression\n    });\n    _react.default.unstable_postpone(reason);\n}\nfunction usedDynamicAPIs(prerenderState) {\n    return prerenderState.dynamicAccesses.length > 0;\n}\nfunction formatDynamicAPIAccesses(prerenderState) {\n    return prerenderState.dynamicAccesses.filter((access)=>typeof access.stack === \"string\" && access.stack.length > 0).map(({ expression, stack })=>{\n        stack = stack.split(\"\\n\")// Remove the \"Error: \" prefix from the first line of the stack trace as\n        // well as the first 4 lines of the stack trace which is the distance\n        // from the user code and the `new Error().stack` call.\n        .slice(4).filter((line)=>{\n            // Exclude Next.js internals from the stack trace.\n            if (line.includes(\"node_modules/next/\")) {\n                return false;\n            }\n            // Exclude anonymous functions from the stack trace.\n            if (line.includes(\" (<anonymous>)\")) {\n                return false;\n            }\n            // Exclude Node.js internals from the stack trace.\n            if (line.includes(\" (node:\")) {\n                return false;\n            }\n            return true;\n        }).join(\"\\n\");\n        return `Dynamic API Usage Debug - ${expression}:\\n${stack}`;\n    });\n}\nfunction assertPostpone() {\n    if (!hasPostpone) {\n        throw new Error(`Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js`);\n    }\n}\nfunction createPostponedAbortSignal(reason) {\n    assertPostpone();\n    const controller = new AbortController();\n    // We get our hands on a postpone instance by calling postpone and catching the throw\n    try {\n        _react.default.unstable_postpone(reason);\n    } catch (x) {\n        controller.abort(x);\n    }\n    return controller.signal;\n}\n\n//# sourceMappingURL=dynamic-rendering.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NlcnZlci9hcHAtcmVuZGVyL2R5bmFtaWMtcmVuZGVyaW5nLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNhO0FBQ2IsOENBQTZDO0FBQzdDO0FBQ0EsQ0FBQyxFQUFDO0FBQ0YsTUFBTSxDQVNMO0FBQ0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNELHNEQUFzRCxtQkFBTyxDQUFDLG9GQUFPO0FBQ3JFLDRCQUE0QixtQkFBTyxDQUFDLDZJQUE4QztBQUNsRixpQ0FBaUMsbUJBQU8sQ0FBQyx1SkFBbUQ7QUFDNUYsYUFBYSxtQkFBTyxDQUFDLCtFQUFlO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOLDBFQUEwRSxVQUFVLCtFQUErRSxXQUFXO0FBQzlLLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBLDRFQUE0RSxVQUFVLGtEQUFrRCxXQUFXO0FBQ25KO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQyxVQUFVLFFBQVEsV0FBVyxtTEFBbUwsV0FBVztBQUM1UCxNQUFNO0FBQ04sMEVBQTBFLFVBQVUsK0VBQStFLFdBQVc7QUFDOUssTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0EsNEVBQTRFLFVBQVUsb0RBQW9ELFdBQVc7QUFDcko7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLGtDQUFrQztBQUN0RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0QkFBNEIsVUFBVSxrRUFBa0UsV0FBVztBQUNuSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLCtIQUErSCxtQkFBbUI7QUFDbEo7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVCw0Q0FBNEMsV0FBVyxLQUFLLE1BQU07QUFDbEUsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9zZXJ2ZXIvYXBwLXJlbmRlci9keW5hbWljLXJlbmRlcmluZy5qcz82ZmFmIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogVGhlIGZ1bmN0aW9ucyBwcm92aWRlZCBieSB0aGlzIG1vZHVsZSBhcmUgdXNlZCB0byBjb21tdW5pY2F0ZSBjZXJ0YWluIHByb3BlcnRpZXNcbiAqIGFib3V0IHRoZSBjdXJyZW50bHkgcnVubmluZyBjb2RlIHNvIHRoYXQgTmV4dC5qcyBjYW4gbWFrZSBkZWNpc2lvbnMgb24gaG93IHRvIGhhbmRsZVxuICogdGhlIGN1cnJlbnQgZXhlY3V0aW9uIGluIGRpZmZlcmVudCByZW5kZXJpbmcgbW9kZXMgc3VjaCBhcyBwcmUtcmVuZGVyaW5nLCByZXN1bWluZywgYW5kIFNTUi5cbiAqXG4gKiBUb2RheSBOZXh0LmpzIHRyZWF0cyBhbGwgY29kZSBhcyBwb3RlbnRpYWxseSBzdGF0aWMuIENlcnRhaW4gQVBJcyBtYXkgb25seSBtYWtlIHNlbnNlIHdoZW4gZHluYW1pY2FsbHkgcmVuZGVyaW5nLlxuICogVHJhZGl0aW9uYWxseSB0aGlzIG1lYW50IGRlb3B0aW5nIHRoZSBlbnRpcmUgcmVuZGVyIHRvIGR5bmFtaWMgaG93ZXZlciB3aXRoIFBQUiB3ZSBjYW4gbm93IGRlb3B0IHBhcnRzXG4gKiBvZiBhIFJlYWN0IHRyZWUgYXMgZHluYW1pYyB3aGlsZSBzdGlsbCBrZWVwaW5nIG90aGVyIHBhcnRzIHN0YXRpYy4gVGhlcmUgYXJlIHJlYWxseSB0d28gZGlmZmVyZW50IGtpbmRzIG9mXG4gKiBEeW5hbWljIGluZGljYXRpb25zLlxuICpcbiAqIFRoZSBmaXJzdCBpcyBzaW1wbHkgYW4gaW50ZW50aW9uIHRvIGJlIGR5bmFtaWMuIHVuc3RhYmxlX25vU3RvcmUgaXMgYW4gZXhhbXBsZSBvZiB0aGlzIHdoZXJlXG4gKiB0aGUgY3VycmVudGx5IGV4ZWN1dGluZyBjb2RlIHNpbXBseSBkZWNsYXJlcyB0aGF0IHRoZSBjdXJyZW50IHNjb3BlIGlzIGR5bmFtaWMgYnV0IGlmIHlvdSB1c2UgaXRcbiAqIGluc2lkZSB1bnN0YWJsZV9jYWNoZSBpdCBjYW4gc3RpbGwgYmUgY2FjaGVkLiBUaGlzIHR5cGUgb2YgaW5kaWNhdGlvbiBjYW4gYmUgcmVtb3ZlZCBpZiB3ZSBldmVyXG4gKiBtYWtlIHRoZSBkZWZhdWx0IGR5bmFtaWMgdG8gYmVnaW4gd2l0aCBiZWNhdXNlIHRoZSBvbmx5IHdheSB5b3Ugd291bGQgZXZlciBiZSBzdGF0aWMgaXMgaW5zaWRlXG4gKiBhIGNhY2hlIHNjb3BlIHdoaWNoIHRoaXMgaW5kaWNhdGlvbiBkb2VzIG5vdCBhZmZlY3QuXG4gKlxuICogVGhlIHNlY29uZCBpcyBhbiBpbmRpY2F0aW9uIHRoYXQgYSBkeW5hbWljIGRhdGEgc291cmNlIHdhcyByZWFkLiBUaGlzIGlzIGEgc3Ryb25nZXIgZm9ybSBvZiBkeW5hbWljXG4gKiBiZWNhdXNlIGl0IG1lYW5zIHRoYXQgaXQgaXMgaW5hcHByb3ByaWF0ZSB0byBjYWNoZSB0aGlzIGF0IGFsbC4gdXNpbmcgYSBkeW5hbWljIGRhdGEgc291cmNlIGluc2lkZVxuICogdW5zdGFibGVfY2FjaGUgc2hvdWxkIGVycm9yLiBJZiB5b3Ugd2FudCB0byB1c2Ugc29tZSBkeW5hbWljIGRhdGEgaW5zaWRlIHVuc3RhYmxlX2NhY2hlIHlvdSBzaG91bGRcbiAqIHJlYWQgdGhhdCBkYXRhIG91dHNpZGUgdGhlIGNhY2hlIGFuZCBwYXNzIGl0IGluIGFzIGFuIGFyZ3VtZW50IHRvIHRoZSBjYWNoZWQgZnVuY3Rpb24uXG4gKi8gLy8gT25jZSBwb3N0cG9uZSBpcyBpbiBzdGFibGUgd2Ugc2hvdWxkIHN3aXRjaCB0byBpbXBvcnRpbmcgdGhlIHBvc3Rwb25lIGV4cG9ydCBkaXJlY3RseVxuXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHtcbiAgICB2YWx1ZTogdHJ1ZVxufSk7XG4wICYmIChtb2R1bGUuZXhwb3J0cyA9IHtcbiAgICBQb3N0cG9uZTogbnVsbCxcbiAgICBjcmVhdGVQb3N0cG9uZWRBYm9ydFNpZ25hbDogbnVsbCxcbiAgICBjcmVhdGVQcmVyZW5kZXJTdGF0ZTogbnVsbCxcbiAgICBmb3JtYXREeW5hbWljQVBJQWNjZXNzZXM6IG51bGwsXG4gICAgbWFya0N1cnJlbnRTY29wZUFzRHluYW1pYzogbnVsbCxcbiAgICB0cmFja0R5bmFtaWNEYXRhQWNjZXNzZWQ6IG51bGwsXG4gICAgdHJhY2tEeW5hbWljRmV0Y2g6IG51bGwsXG4gICAgdXNlZER5bmFtaWNBUElzOiBudWxsXG59KTtcbmZ1bmN0aW9uIF9leHBvcnQodGFyZ2V0LCBhbGwpIHtcbiAgICBmb3IodmFyIG5hbWUgaW4gYWxsKU9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0YXJnZXQsIG5hbWUsIHtcbiAgICAgICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICAgICAgZ2V0OiBhbGxbbmFtZV1cbiAgICB9KTtcbn1cbl9leHBvcnQoZXhwb3J0cywge1xuICAgIFBvc3Rwb25lOiBmdW5jdGlvbigpIHtcbiAgICAgICAgcmV0dXJuIFBvc3Rwb25lO1xuICAgIH0sXG4gICAgY3JlYXRlUG9zdHBvbmVkQWJvcnRTaWduYWw6IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gY3JlYXRlUG9zdHBvbmVkQWJvcnRTaWduYWw7XG4gICAgfSxcbiAgICBjcmVhdGVQcmVyZW5kZXJTdGF0ZTogZnVuY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiBjcmVhdGVQcmVyZW5kZXJTdGF0ZTtcbiAgICB9LFxuICAgIGZvcm1hdER5bmFtaWNBUElBY2Nlc3NlczogZnVuY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiBmb3JtYXREeW5hbWljQVBJQWNjZXNzZXM7XG4gICAgfSxcbiAgICBtYXJrQ3VycmVudFNjb3BlQXNEeW5hbWljOiBmdW5jdGlvbigpIHtcbiAgICAgICAgcmV0dXJuIG1hcmtDdXJyZW50U2NvcGVBc0R5bmFtaWM7XG4gICAgfSxcbiAgICB0cmFja0R5bmFtaWNEYXRhQWNjZXNzZWQ6IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gdHJhY2tEeW5hbWljRGF0YUFjY2Vzc2VkO1xuICAgIH0sXG4gICAgdHJhY2tEeW5hbWljRmV0Y2g6IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gdHJhY2tEeW5hbWljRmV0Y2g7XG4gICAgfSxcbiAgICB1c2VkRHluYW1pY0FQSXM6IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gdXNlZER5bmFtaWNBUElzO1xuICAgIH1cbn0pO1xuY29uc3QgX3JlYWN0ID0gLyojX19QVVJFX18qLyBfaW50ZXJvcF9yZXF1aXJlX2RlZmF1bHQocmVxdWlyZShcInJlYWN0XCIpKTtcbmNvbnN0IF9ob29rc3NlcnZlcmNvbnRleHQgPSByZXF1aXJlKFwiLi4vLi4vY2xpZW50L2NvbXBvbmVudHMvaG9va3Mtc2VydmVyLWNvbnRleHRcIik7XG5jb25zdCBfc3RhdGljZ2VuZXJhdGlvbmJhaWxvdXQgPSByZXF1aXJlKFwiLi4vLi4vY2xpZW50L2NvbXBvbmVudHMvc3RhdGljLWdlbmVyYXRpb24tYmFpbG91dFwiKTtcbmNvbnN0IF91cmwgPSByZXF1aXJlKFwiLi4vLi4vbGliL3VybFwiKTtcbmZ1bmN0aW9uIF9pbnRlcm9wX3JlcXVpcmVfZGVmYXVsdChvYmopIHtcbiAgICByZXR1cm4gb2JqICYmIG9iai5fX2VzTW9kdWxlID8gb2JqIDoge1xuICAgICAgICBkZWZhdWx0OiBvYmpcbiAgICB9O1xufVxuY29uc3QgaGFzUG9zdHBvbmUgPSB0eXBlb2YgX3JlYWN0LmRlZmF1bHQudW5zdGFibGVfcG9zdHBvbmUgPT09IFwiZnVuY3Rpb25cIjtcbmZ1bmN0aW9uIGNyZWF0ZVByZXJlbmRlclN0YXRlKGlzRGVidWdTa2VsZXRvbikge1xuICAgIHJldHVybiB7XG4gICAgICAgIGlzRGVidWdTa2VsZXRvbixcbiAgICAgICAgZHluYW1pY0FjY2Vzc2VzOiBbXVxuICAgIH07XG59XG5mdW5jdGlvbiBtYXJrQ3VycmVudFNjb3BlQXNEeW5hbWljKHN0b3JlLCBleHByZXNzaW9uKSB7XG4gICAgY29uc3QgcGF0aG5hbWUgPSAoMCwgX3VybC5nZXRQYXRobmFtZSkoc3RvcmUudXJsUGF0aG5hbWUpO1xuICAgIGlmIChzdG9yZS5pc1Vuc3RhYmxlQ2FjaGVDYWxsYmFjaykge1xuICAgICAgICAvLyBpbnNpZGUgY2FjaGUgc2NvcGVzIG1hcmtpbmcgYSBzY29wZSBhcyBkeW5hbWljIGhhcyBubyBlZmZlY3QgYmVjYXVzZSB0aGUgb3V0ZXIgY2FjaGUgc2NvcGVcbiAgICAgICAgLy8gY3JlYXRlcyBhIGNhY2hlIGJvdW5kYXJ5LiBUaGlzIGlzIHN1YnRseSBkaWZmZXJlbnQgZnJvbSByZWFkaW5nIGEgZHluYW1pYyBkYXRhIHNvdXJjZSB3aGljaCBpc1xuICAgICAgICAvLyBmb3JiaWRkZW4gaW5zaWRlIGEgY2FjaGUgc2NvcGUuXG4gICAgICAgIHJldHVybjtcbiAgICB9IGVsc2UgaWYgKHN0b3JlLmR5bmFtaWNTaG91bGRFcnJvcikge1xuICAgICAgICB0aHJvdyBuZXcgX3N0YXRpY2dlbmVyYXRpb25iYWlsb3V0LlN0YXRpY0dlbkJhaWxvdXRFcnJvcihgUm91dGUgJHtwYXRobmFtZX0gd2l0aCBcXGBkeW5hbWljID0gXCJlcnJvclwiXFxgIGNvdWxkbid0IGJlIHJlbmRlcmVkIHN0YXRpY2FsbHkgYmVjYXVzZSBpdCB1c2VkIFxcYCR7ZXhwcmVzc2lvbn1cXGAuIFNlZSBtb3JlIGluZm8gaGVyZTogaHR0cHM6Ly9uZXh0anMub3JnL2RvY3MvYXBwL2J1aWxkaW5nLXlvdXItYXBwbGljYXRpb24vcmVuZGVyaW5nL3N0YXRpYy1hbmQtZHluYW1pYyNkeW5hbWljLXJlbmRlcmluZ2ApO1xuICAgIH0gZWxzZSBpZiAoLy8gV2UgYXJlIGluIGEgcHJlcmVuZGVyIChQUFIgZW5hYmxlZCwgZHVyaW5nIGJ1aWxkKVxuICAgIHN0b3JlLnByZXJlbmRlclN0YXRlKSB7XG4gICAgICAgIC8vIFdlIHRyYWNrIHRoYXQgd2UgaGFkIGEgZHluYW1pYyBzY29wZSB0aGF0IHBvc3Rwb25lZC5cbiAgICAgICAgLy8gVGhpcyB3aWxsIGJlIHVzZWQgYnkgdGhlIHJlbmRlcmVyIHRvIGRlY2lkZSB3aGV0aGVyXG4gICAgICAgIC8vIHRoZSBwcmVyZW5kZXIgcmVxdWlyZXMgYSByZXN1bWVcbiAgICAgICAgcG9zdHBvbmVXaXRoVHJhY2tpbmcoc3RvcmUucHJlcmVuZGVyU3RhdGUsIGV4cHJlc3Npb24sIHBhdGhuYW1lKTtcbiAgICB9IGVsc2Uge1xuICAgICAgICBzdG9yZS5yZXZhbGlkYXRlID0gMDtcbiAgICAgICAgaWYgKHN0b3JlLmlzU3RhdGljR2VuZXJhdGlvbikge1xuICAgICAgICAgICAgLy8gV2UgYXJlbid0IHByZXJlbmRlcmluZyBidXQgd2UgYXJlIGdlbmVyYXRpbmcgYSBzdGF0aWMgcGFnZS4gV2UgbmVlZCB0byBiYWlsIG91dCBvZiBzdGF0aWMgZ2VuZXJhdGlvblxuICAgICAgICAgICAgY29uc3QgZXJyID0gbmV3IF9ob29rc3NlcnZlcmNvbnRleHQuRHluYW1pY1NlcnZlckVycm9yKGBSb3V0ZSAke3BhdGhuYW1lfSBjb3VsZG4ndCBiZSByZW5kZXJlZCBzdGF0aWNhbGx5IGJlY2F1c2UgaXQgdXNlZCAke2V4cHJlc3Npb259LiBTZWUgbW9yZSBpbmZvIGhlcmU6IGh0dHBzOi8vbmV4dGpzLm9yZy9kb2NzL21lc3NhZ2VzL2R5bmFtaWMtc2VydmVyLWVycm9yYCk7XG4gICAgICAgICAgICBzdG9yZS5keW5hbWljVXNhZ2VEZXNjcmlwdGlvbiA9IGV4cHJlc3Npb247XG4gICAgICAgICAgICBzdG9yZS5keW5hbWljVXNhZ2VTdGFjayA9IGVyci5zdGFjaztcbiAgICAgICAgICAgIHRocm93IGVycjtcbiAgICAgICAgfVxuICAgIH1cbn1cbmZ1bmN0aW9uIHRyYWNrRHluYW1pY0RhdGFBY2Nlc3NlZChzdG9yZSwgZXhwcmVzc2lvbikge1xuICAgIGNvbnN0IHBhdGhuYW1lID0gKDAsIF91cmwuZ2V0UGF0aG5hbWUpKHN0b3JlLnVybFBhdGhuYW1lKTtcbiAgICBpZiAoc3RvcmUuaXNVbnN0YWJsZUNhY2hlQ2FsbGJhY2spIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBSb3V0ZSAke3BhdGhuYW1lfSB1c2VkIFwiJHtleHByZXNzaW9ufVwiIGluc2lkZSBhIGZ1bmN0aW9uIGNhY2hlZCB3aXRoIFwidW5zdGFibGVfY2FjaGUoLi4uKVwiLiBBY2Nlc3NpbmcgRHluYW1pYyBkYXRhIHNvdXJjZXMgaW5zaWRlIGEgY2FjaGUgc2NvcGUgaXMgbm90IHN1cHBvcnRlZC4gSWYgeW91IG5lZWQgdGhpcyBkYXRhIGluc2lkZSBhIGNhY2hlZCBmdW5jdGlvbiB1c2UgXCIke2V4cHJlc3Npb259XCIgb3V0c2lkZSBvZiB0aGUgY2FjaGVkIGZ1bmN0aW9uIGFuZCBwYXNzIHRoZSByZXF1aXJlZCBkeW5hbWljIGRhdGEgaW4gYXMgYW4gYXJndW1lbnQuIFNlZSBtb3JlIGluZm8gaGVyZTogaHR0cHM6Ly9uZXh0anMub3JnL2RvY3MvYXBwL2FwaS1yZWZlcmVuY2UvZnVuY3Rpb25zL3Vuc3RhYmxlX2NhY2hlYCk7XG4gICAgfSBlbHNlIGlmIChzdG9yZS5keW5hbWljU2hvdWxkRXJyb3IpIHtcbiAgICAgICAgdGhyb3cgbmV3IF9zdGF0aWNnZW5lcmF0aW9uYmFpbG91dC5TdGF0aWNHZW5CYWlsb3V0RXJyb3IoYFJvdXRlICR7cGF0aG5hbWV9IHdpdGggXFxgZHluYW1pYyA9IFwiZXJyb3JcIlxcYCBjb3VsZG4ndCBiZSByZW5kZXJlZCBzdGF0aWNhbGx5IGJlY2F1c2UgaXQgdXNlZCBcXGAke2V4cHJlc3Npb259XFxgLiBTZWUgbW9yZSBpbmZvIGhlcmU6IGh0dHBzOi8vbmV4dGpzLm9yZy9kb2NzL2FwcC9idWlsZGluZy15b3VyLWFwcGxpY2F0aW9uL3JlbmRlcmluZy9zdGF0aWMtYW5kLWR5bmFtaWMjZHluYW1pYy1yZW5kZXJpbmdgKTtcbiAgICB9IGVsc2UgaWYgKC8vIFdlIGFyZSBpbiBhIHByZXJlbmRlciAoUFBSIGVuYWJsZWQsIGR1cmluZyBidWlsZClcbiAgICBzdG9yZS5wcmVyZW5kZXJTdGF0ZSkge1xuICAgICAgICAvLyBXZSB0cmFjayB0aGF0IHdlIGhhZCBhIGR5bmFtaWMgc2NvcGUgdGhhdCBwb3N0cG9uZWQuXG4gICAgICAgIC8vIFRoaXMgd2lsbCBiZSB1c2VkIGJ5IHRoZSByZW5kZXJlciB0byBkZWNpZGUgd2hldGhlclxuICAgICAgICAvLyB0aGUgcHJlcmVuZGVyIHJlcXVpcmVzIGEgcmVzdW1lXG4gICAgICAgIHBvc3Rwb25lV2l0aFRyYWNraW5nKHN0b3JlLnByZXJlbmRlclN0YXRlLCBleHByZXNzaW9uLCBwYXRobmFtZSk7XG4gICAgfSBlbHNlIHtcbiAgICAgICAgc3RvcmUucmV2YWxpZGF0ZSA9IDA7XG4gICAgICAgIGlmIChzdG9yZS5pc1N0YXRpY0dlbmVyYXRpb24pIHtcbiAgICAgICAgICAgIC8vIFdlIGFyZW4ndCBwcmVyZW5kZXJpbmcgYnV0IHdlIGFyZSBnZW5lcmF0aW5nIGEgc3RhdGljIHBhZ2UuIFdlIG5lZWQgdG8gYmFpbCBvdXQgb2Ygc3RhdGljIGdlbmVyYXRpb25cbiAgICAgICAgICAgIGNvbnN0IGVyciA9IG5ldyBfaG9va3NzZXJ2ZXJjb250ZXh0LkR5bmFtaWNTZXJ2ZXJFcnJvcihgUm91dGUgJHtwYXRobmFtZX0gY291bGRuJ3QgYmUgcmVuZGVyZWQgc3RhdGljYWxseSBiZWNhdXNlIGl0IHVzZWQgXFxgJHtleHByZXNzaW9ufVxcYC4gU2VlIG1vcmUgaW5mbyBoZXJlOiBodHRwczovL25leHRqcy5vcmcvZG9jcy9tZXNzYWdlcy9keW5hbWljLXNlcnZlci1lcnJvcmApO1xuICAgICAgICAgICAgc3RvcmUuZHluYW1pY1VzYWdlRGVzY3JpcHRpb24gPSBleHByZXNzaW9uO1xuICAgICAgICAgICAgc3RvcmUuZHluYW1pY1VzYWdlU3RhY2sgPSBlcnIuc3RhY2s7XG4gICAgICAgICAgICB0aHJvdyBlcnI7XG4gICAgICAgIH1cbiAgICB9XG59XG5mdW5jdGlvbiBQb3N0cG9uZSh7IHJlYXNvbiwgcHJlcmVuZGVyU3RhdGUsIHBhdGhuYW1lIH0pIHtcbiAgICBwb3N0cG9uZVdpdGhUcmFja2luZyhwcmVyZW5kZXJTdGF0ZSwgcmVhc29uLCBwYXRobmFtZSk7XG59XG5mdW5jdGlvbiB0cmFja0R5bmFtaWNGZXRjaChzdG9yZSwgZXhwcmVzc2lvbikge1xuICAgIGlmIChzdG9yZS5wcmVyZW5kZXJTdGF0ZSkge1xuICAgICAgICBwb3N0cG9uZVdpdGhUcmFja2luZyhzdG9yZS5wcmVyZW5kZXJTdGF0ZSwgZXhwcmVzc2lvbiwgc3RvcmUudXJsUGF0aG5hbWUpO1xuICAgIH1cbn1cbmZ1bmN0aW9uIHBvc3Rwb25lV2l0aFRyYWNraW5nKHByZXJlbmRlclN0YXRlLCBleHByZXNzaW9uLCBwYXRobmFtZSkge1xuICAgIGFzc2VydFBvc3Rwb25lKCk7XG4gICAgY29uc3QgcmVhc29uID0gYFJvdXRlICR7cGF0aG5hbWV9IG5lZWRzIHRvIGJhaWwgb3V0IG9mIHByZXJlbmRlcmluZyBhdCB0aGlzIHBvaW50IGJlY2F1c2UgaXQgdXNlZCAke2V4cHJlc3Npb259LiBgICsgYFJlYWN0IHRocm93cyB0aGlzIHNwZWNpYWwgb2JqZWN0IHRvIGluZGljYXRlIHdoZXJlLiBJdCBzaG91bGQgbm90IGJlIGNhdWdodCBieSBgICsgYHlvdXIgb3duIHRyeS9jYXRjaC4gTGVhcm4gbW9yZTogaHR0cHM6Ly9uZXh0anMub3JnL2RvY3MvbWVzc2FnZXMvcHByLWNhdWdodC1lcnJvcmA7XG4gICAgcHJlcmVuZGVyU3RhdGUuZHluYW1pY0FjY2Vzc2VzLnB1c2goe1xuICAgICAgICAvLyBXaGVuIHdlIGFyZW4ndCBkZWJ1Z2dpbmcsIHdlIGRvbid0IG5lZWQgdG8gY3JlYXRlIGFub3RoZXIgZXJyb3IgZm9yIHRoZVxuICAgICAgICAvLyBzdGFjayB0cmFjZS5cbiAgICAgICAgc3RhY2s6IHByZXJlbmRlclN0YXRlLmlzRGVidWdTa2VsZXRvbiA/IG5ldyBFcnJvcigpLnN0YWNrIDogdW5kZWZpbmVkLFxuICAgICAgICBleHByZXNzaW9uXG4gICAgfSk7XG4gICAgX3JlYWN0LmRlZmF1bHQudW5zdGFibGVfcG9zdHBvbmUocmVhc29uKTtcbn1cbmZ1bmN0aW9uIHVzZWREeW5hbWljQVBJcyhwcmVyZW5kZXJTdGF0ZSkge1xuICAgIHJldHVybiBwcmVyZW5kZXJTdGF0ZS5keW5hbWljQWNjZXNzZXMubGVuZ3RoID4gMDtcbn1cbmZ1bmN0aW9uIGZvcm1hdER5bmFtaWNBUElBY2Nlc3NlcyhwcmVyZW5kZXJTdGF0ZSkge1xuICAgIHJldHVybiBwcmVyZW5kZXJTdGF0ZS5keW5hbWljQWNjZXNzZXMuZmlsdGVyKChhY2Nlc3MpPT50eXBlb2YgYWNjZXNzLnN0YWNrID09PSBcInN0cmluZ1wiICYmIGFjY2Vzcy5zdGFjay5sZW5ndGggPiAwKS5tYXAoKHsgZXhwcmVzc2lvbiwgc3RhY2sgfSk9PntcbiAgICAgICAgc3RhY2sgPSBzdGFjay5zcGxpdChcIlxcblwiKS8vIFJlbW92ZSB0aGUgXCJFcnJvcjogXCIgcHJlZml4IGZyb20gdGhlIGZpcnN0IGxpbmUgb2YgdGhlIHN0YWNrIHRyYWNlIGFzXG4gICAgICAgIC8vIHdlbGwgYXMgdGhlIGZpcnN0IDQgbGluZXMgb2YgdGhlIHN0YWNrIHRyYWNlIHdoaWNoIGlzIHRoZSBkaXN0YW5jZVxuICAgICAgICAvLyBmcm9tIHRoZSB1c2VyIGNvZGUgYW5kIHRoZSBgbmV3IEVycm9yKCkuc3RhY2tgIGNhbGwuXG4gICAgICAgIC5zbGljZSg0KS5maWx0ZXIoKGxpbmUpPT57XG4gICAgICAgICAgICAvLyBFeGNsdWRlIE5leHQuanMgaW50ZXJuYWxzIGZyb20gdGhlIHN0YWNrIHRyYWNlLlxuICAgICAgICAgICAgaWYgKGxpbmUuaW5jbHVkZXMoXCJub2RlX21vZHVsZXMvbmV4dC9cIikpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyBFeGNsdWRlIGFub255bW91cyBmdW5jdGlvbnMgZnJvbSB0aGUgc3RhY2sgdHJhY2UuXG4gICAgICAgICAgICBpZiAobGluZS5pbmNsdWRlcyhcIiAoPGFub255bW91cz4pXCIpKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgLy8gRXhjbHVkZSBOb2RlLmpzIGludGVybmFscyBmcm9tIHRoZSBzdGFjayB0cmFjZS5cbiAgICAgICAgICAgIGlmIChsaW5lLmluY2x1ZGVzKFwiIChub2RlOlwiKSkge1xuICAgICAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiB0cnVlO1xuICAgICAgICB9KS5qb2luKFwiXFxuXCIpO1xuICAgICAgICByZXR1cm4gYER5bmFtaWMgQVBJIFVzYWdlIERlYnVnIC0gJHtleHByZXNzaW9ufTpcXG4ke3N0YWNrfWA7XG4gICAgfSk7XG59XG5mdW5jdGlvbiBhc3NlcnRQb3N0cG9uZSgpIHtcbiAgICBpZiAoIWhhc1Bvc3Rwb25lKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgSW52YXJpYW50OiBSZWFjdC51bnN0YWJsZV9wb3N0cG9uZSBpcyBub3QgZGVmaW5lZC4gVGhpcyBzdWdnZXN0cyB0aGUgd3JvbmcgdmVyc2lvbiBvZiBSZWFjdCB3YXMgbG9hZGVkLiBUaGlzIGlzIGEgYnVnIGluIE5leHQuanNgKTtcbiAgICB9XG59XG5mdW5jdGlvbiBjcmVhdGVQb3N0cG9uZWRBYm9ydFNpZ25hbChyZWFzb24pIHtcbiAgICBhc3NlcnRQb3N0cG9uZSgpO1xuICAgIGNvbnN0IGNvbnRyb2xsZXIgPSBuZXcgQWJvcnRDb250cm9sbGVyKCk7XG4gICAgLy8gV2UgZ2V0IG91ciBoYW5kcyBvbiBhIHBvc3Rwb25lIGluc3RhbmNlIGJ5IGNhbGxpbmcgcG9zdHBvbmUgYW5kIGNhdGNoaW5nIHRoZSB0aHJvd1xuICAgIHRyeSB7XG4gICAgICAgIF9yZWFjdC5kZWZhdWx0LnVuc3RhYmxlX3Bvc3Rwb25lKHJlYXNvbik7XG4gICAgfSBjYXRjaCAoeCkge1xuICAgICAgICBjb250cm9sbGVyLmFib3J0KHgpO1xuICAgIH1cbiAgICByZXR1cm4gY29udHJvbGxlci5zaWduYWw7XG59XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWR5bmFtaWMtcmVuZGVyaW5nLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/next/dist/server/app-render/dynamic-rendering.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/next/dist/server/web/spec-extension/adapters/reflect.js":
/*!*******************************************************************************!*\
  !*** ../node_modules/next/dist/server/web/spec-extension/adapters/reflect.js ***!
  \*******************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"ReflectAdapter\", ({\n    enumerable: true,\n    get: function() {\n        return ReflectAdapter;\n    }\n}));\nclass ReflectAdapter {\n    static get(target, prop, receiver) {\n        const value = Reflect.get(target, prop, receiver);\n        if (typeof value === \"function\") {\n            return value.bind(target);\n        }\n        return value;\n    }\n    static set(target, prop, value, receiver) {\n        return Reflect.set(target, prop, value, receiver);\n    }\n    static has(target, prop) {\n        return Reflect.has(target, prop);\n    }\n    static deleteProperty(target, prop) {\n        return Reflect.deleteProperty(target, prop);\n    }\n}\n\n//# sourceMappingURL=reflect.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NlcnZlci93ZWIvc3BlYy1leHRlbnNpb24vYWRhcHRlcnMvcmVmbGVjdC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QztBQUM3QztBQUNBLENBQUMsRUFBQztBQUNGLGtEQUFpRDtBQUNqRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsRUFBQztBQUNGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9zZXJ2ZXIvd2ViL3NwZWMtZXh0ZW5zaW9uL2FkYXB0ZXJzL3JlZmxlY3QuanM/NjE1NiJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICAgIHZhbHVlOiB0cnVlXG59KTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIlJlZmxlY3RBZGFwdGVyXCIsIHtcbiAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgIGdldDogZnVuY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiBSZWZsZWN0QWRhcHRlcjtcbiAgICB9XG59KTtcbmNsYXNzIFJlZmxlY3RBZGFwdGVyIHtcbiAgICBzdGF0aWMgZ2V0KHRhcmdldCwgcHJvcCwgcmVjZWl2ZXIpIHtcbiAgICAgICAgY29uc3QgdmFsdWUgPSBSZWZsZWN0LmdldCh0YXJnZXQsIHByb3AsIHJlY2VpdmVyKTtcbiAgICAgICAgaWYgKHR5cGVvZiB2YWx1ZSA9PT0gXCJmdW5jdGlvblwiKSB7XG4gICAgICAgICAgICByZXR1cm4gdmFsdWUuYmluZCh0YXJnZXQpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiB2YWx1ZTtcbiAgICB9XG4gICAgc3RhdGljIHNldCh0YXJnZXQsIHByb3AsIHZhbHVlLCByZWNlaXZlcikge1xuICAgICAgICByZXR1cm4gUmVmbGVjdC5zZXQodGFyZ2V0LCBwcm9wLCB2YWx1ZSwgcmVjZWl2ZXIpO1xuICAgIH1cbiAgICBzdGF0aWMgaGFzKHRhcmdldCwgcHJvcCkge1xuICAgICAgICByZXR1cm4gUmVmbGVjdC5oYXModGFyZ2V0LCBwcm9wKTtcbiAgICB9XG4gICAgc3RhdGljIGRlbGV0ZVByb3BlcnR5KHRhcmdldCwgcHJvcCkge1xuICAgICAgICByZXR1cm4gUmVmbGVjdC5kZWxldGVQcm9wZXJ0eSh0YXJnZXQsIHByb3ApO1xuICAgIH1cbn1cblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cmVmbGVjdC5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/next/dist/server/web/spec-extension/adapters/reflect.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/next/dist/client/components/client-page.js":
/*!******************************************************************!*\
  !*** ../node_modules/next/dist/client/components/client-page.js ***!
  \******************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"ClientPageRoot\", ({\n    enumerable: true,\n    get: function() {\n        return ClientPageRoot;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _searchparams = __webpack_require__(/*! ./search-params */ \"(app-pages-browser)/../node_modules/next/dist/client/components/search-params.js\");\nfunction ClientPageRoot(param) {\n    let { Component, props } = param;\n    // We expect to be passed searchParams but even if we aren't we can construct one from\n    // an empty object. We only do this if we are in a static generation as a performance\n    // optimization. Ideally we'd unconditionally construct the tracked params but since\n    // this creates a proxy which is slow and this would happen even for client navigations\n    // that are done entirely dynamically and we know there the dynamic tracking is a noop\n    // in this dynamic case we can safely elide it.\n    props.searchParams = (0, _searchparams.createDynamicallyTrackedSearchParams)(props.searchParams || {});\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(Component, {\n        ...props\n    });\n}\n_c = ClientPageRoot;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=client-page.js.map\nvar _c;\n$RefreshReg$(_c, \"ClientPageRoot\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2NsaWVudC1wYWdlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBR08sTUFBQUEsZ0JBQVNDLG1CQUFBQSxDQU1mO1NBTjhCQSxlQUU3QkMsS0FJRDtJQUNDLE1BQUFDLFNBQUEsRUFBQUQsS0FBQSxLQUFBRTtJQUNBLHNGQUFxRjtJQUNyRixxRkFBb0Y7SUFDcEY7SUFDQSx1RkFBc0Y7SUFDdEYsc0ZBQStDO0lBQy9DRiwrQ0FBcUJHO0lBR3JCSCxNQUFBSSxZQUFBLEdBQU8sSUFBQU4sY0FBQUssb0NBQUNGLEVBQUFBLE1BQUFBLFlBQUFBLElBQUFBLENBQUFBO1dBQW1CLGtCQUFBSSxZQUFBQyxHQUFBLEVBQUFMLFdBQUE7O0lBQzdCOztLQWpCK0JGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi8uLi9zcmMvY2xpZW50L2NvbXBvbmVudHMvY2xpZW50LXBhZ2UudHN4Pzg0YzUiXSwibmFtZXMiOlsiX3NlYXJjaHBhcmFtcyIsIkNsaWVudFBhZ2VSb290IiwicHJvcHMiLCJDb21wb25lbnQiLCJwYXJhbSIsImNyZWF0ZUR5bmFtaWNhbGx5VHJhY2tlZFNlYXJjaFBhcmFtcyIsInNlYXJjaFBhcmFtcyIsIl9qc3hydW50aW1lIiwianN4Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/next/dist/client/components/client-page.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/next/dist/client/components/hooks-server-context.js":
/*!***************************************************************************!*\
  !*** ../node_modules/next/dist/client/components/hooks-server-context.js ***!
  \***************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DynamicServerError: function() {\n        return DynamicServerError;\n    },\n    isDynamicServerError: function() {\n        return isDynamicServerError;\n    }\n});\nconst DYNAMIC_ERROR_CODE = \"DYNAMIC_SERVER_USAGE\";\nclass DynamicServerError extends Error {\n    constructor(description){\n        super(\"Dynamic server usage: \" + description);\n        this.description = description;\n        this.digest = DYNAMIC_ERROR_CODE;\n    }\n}\nfunction isDynamicServerError(err) {\n    if (typeof err !== \"object\" || err === null || !(\"digest\" in err) || typeof err.digest !== \"string\") {\n        return false;\n    }\n    return err.digest === DYNAMIC_ERROR_CODE;\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=hooks-server-context.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2hvb2tzLXNlcnZlci1jb250ZXh0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztJQUVhQSxvQkFBa0I7ZUFBbEJBOztJQVFHQyxzQkFBb0I7ZUFBcEJBOzs7QUFWaEIsTUFBTUMscUJBQXFCO0FBRXBCLE1BQU1GLDJCQUEyQkc7SUFHdENDLFlBQVlDLFdBQW1DLENBQUU7UUFDL0MsS0FBSyxDQUFDLDJCQUF5QkE7YUFETEEsV0FBQUEsR0FBQUE7YUFGNUJDLE1BQUFBLEdBQW9DSjtJQUlwQztBQUNGO0FBRU8sU0FBU0QscUJBQXFCTSxHQUFZO0lBQy9DLElBQ0UsT0FBT0EsUUFBUSxZQUNmQSxRQUFRLFFBQ1IsQ0FBRSxhQUFZQSxHQUFBQSxLQUNkLE9BQU9BLElBQUlELE1BQU0sS0FBSyxVQUN0QjtRQUNBLE9BQU87SUFDVDtJQUVBLE9BQU9DLElBQUlELE1BQU0sS0FBS0o7QUFDeEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uLy4uL3NyYy9jbGllbnQvY29tcG9uZW50cy9ob29rcy1zZXJ2ZXItY29udGV4dC50cz84MjM4Il0sIm5hbWVzIjpbIkR5bmFtaWNTZXJ2ZXJFcnJvciIsImlzRHluYW1pY1NlcnZlckVycm9yIiwiRFlOQU1JQ19FUlJPUl9DT0RFIiwiRXJyb3IiLCJjb25zdHJ1Y3RvciIsImRlc2NyaXB0aW9uIiwiZGlnZXN0IiwiZXJyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/next/dist/client/components/hooks-server-context.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/next/dist/client/components/layout-router.js":
/*!********************************************************************!*\
  !*** ../node_modules/next/dist/client/components/layout-router.js ***!
  \********************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return OuterLayoutRouter;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/../node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/../node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/index.js\"));\nconst _reactdom = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react-dom */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react-dom/index.js\"));\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/../node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _fetchserverresponse = __webpack_require__(/*! ./router-reducer/fetch-server-response */ \"(app-pages-browser)/../node_modules/next/dist/client/components/router-reducer/fetch-server-response.js\");\nconst _unresolvedthenable = __webpack_require__(/*! ./unresolved-thenable */ \"(app-pages-browser)/../node_modules/next/dist/client/components/unresolved-thenable.js\");\nconst _errorboundary = __webpack_require__(/*! ./error-boundary */ \"(app-pages-browser)/../node_modules/next/dist/client/components/error-boundary.js\");\nconst _matchsegments = __webpack_require__(/*! ./match-segments */ \"(app-pages-browser)/../node_modules/next/dist/client/components/match-segments.js\");\nconst _handlesmoothscroll = __webpack_require__(/*! ../../shared/lib/router/utils/handle-smooth-scroll */ \"(app-pages-browser)/../node_modules/next/dist/shared/lib/router/utils/handle-smooth-scroll.js\");\nconst _redirectboundary = __webpack_require__(/*! ./redirect-boundary */ \"(app-pages-browser)/../node_modules/next/dist/client/components/redirect-boundary.js\");\nconst _notfoundboundary = __webpack_require__(/*! ./not-found-boundary */ \"(app-pages-browser)/../node_modules/next/dist/client/components/not-found-boundary.js\");\nconst _getsegmentvalue = __webpack_require__(/*! ./router-reducer/reducers/get-segment-value */ \"(app-pages-browser)/../node_modules/next/dist/client/components/router-reducer/reducers/get-segment-value.js\");\nconst _createroutercachekey = __webpack_require__(/*! ./router-reducer/create-router-cache-key */ \"(app-pages-browser)/../node_modules/next/dist/client/components/router-reducer/create-router-cache-key.js\");\nconst _hasinterceptionrouteincurrenttree = __webpack_require__(/*! ./router-reducer/reducers/has-interception-route-in-current-tree */ \"(app-pages-browser)/../node_modules/next/dist/client/components/router-reducer/reducers/has-interception-route-in-current-tree.js\");\n/**\n * Add refetch marker to router state at the point of the current layout segment.\n * This ensures the response returned is not further down than the current layout segment.\n */ function walkAddRefetch(segmentPathToWalk, treeToRecreate) {\n    if (segmentPathToWalk) {\n        const [segment, parallelRouteKey] = segmentPathToWalk;\n        const isLast = segmentPathToWalk.length === 2;\n        if ((0, _matchsegments.matchSegment)(treeToRecreate[0], segment)) {\n            if (treeToRecreate[1].hasOwnProperty(parallelRouteKey)) {\n                if (isLast) {\n                    const subTree = walkAddRefetch(undefined, treeToRecreate[1][parallelRouteKey]);\n                    return [\n                        treeToRecreate[0],\n                        {\n                            ...treeToRecreate[1],\n                            [parallelRouteKey]: [\n                                subTree[0],\n                                subTree[1],\n                                subTree[2],\n                                \"refetch\"\n                            ]\n                        }\n                    ];\n                }\n                return [\n                    treeToRecreate[0],\n                    {\n                        ...treeToRecreate[1],\n                        [parallelRouteKey]: walkAddRefetch(segmentPathToWalk.slice(2), treeToRecreate[1][parallelRouteKey])\n                    }\n                ];\n            }\n        }\n    }\n    return treeToRecreate;\n}\n// TODO-APP: Replace with new React API for finding dom nodes without a `ref` when available\n/**\n * Wraps ReactDOM.findDOMNode with additional logic to hide React Strict Mode warning\n */ function findDOMNode(instance) {\n    // Tree-shake for server bundle\n    if (typeof window === \"undefined\") return null;\n    // Only apply strict mode warning when not in production\n    if (true) {\n        const originalConsoleError = console.error;\n        try {\n            console.error = function() {\n                for(var _len = arguments.length, messages = new Array(_len), _key = 0; _key < _len; _key++){\n                    messages[_key] = arguments[_key];\n                }\n                // Ignore strict mode warning for the findDomNode call below\n                if (!messages[0].includes(\"Warning: %s is deprecated in StrictMode.\")) {\n                    originalConsoleError(...messages);\n                }\n            };\n            return _reactdom.default.findDOMNode(instance);\n        } finally{\n            console.error = originalConsoleError;\n        }\n    }\n    return _reactdom.default.findDOMNode(instance);\n}\nconst rectProperties = [\n    \"bottom\",\n    \"height\",\n    \"left\",\n    \"right\",\n    \"top\",\n    \"width\",\n    \"x\",\n    \"y\"\n];\n/**\n * Check if a HTMLElement is hidden or fixed/sticky position\n */ function shouldSkipElement(element) {\n    // we ignore fixed or sticky positioned elements since they'll likely pass the \"in-viewport\" check\n    // and will result in a situation we bail on scroll because of something like a fixed nav,\n    // even though the actual page content is offscreen\n    if ([\n        \"sticky\",\n        \"fixed\"\n    ].includes(getComputedStyle(element).position)) {\n        if (true) {\n            console.warn(\"Skipping auto-scroll behavior due to `position: sticky` or `position: fixed` on element:\", element);\n        }\n        return true;\n    }\n    // Uses `getBoundingClientRect` to check if the element is hidden instead of `offsetParent`\n    // because `offsetParent` doesn't consider document/body\n    const rect = element.getBoundingClientRect();\n    return rectProperties.every((item)=>rect[item] === 0);\n}\n/**\n * Check if the top corner of the HTMLElement is in the viewport.\n */ function topOfElementInViewport(element, viewportHeight) {\n    const rect = element.getBoundingClientRect();\n    return rect.top >= 0 && rect.top <= viewportHeight;\n}\n/**\n * Find the DOM node for a hash fragment.\n * If `top` the page has to scroll to the top of the page. This mirrors the browser's behavior.\n * If the hash fragment is an id, the page has to scroll to the element with that id.\n * If the hash fragment is a name, the page has to scroll to the first element with that name.\n */ function getHashFragmentDomNode(hashFragment) {\n    // If the hash fragment is `top` the page has to scroll to the top of the page.\n    if (hashFragment === \"top\") {\n        return document.body;\n    }\n    var _document_getElementById;\n    // If the hash fragment is an id, the page has to scroll to the element with that id.\n    return (_document_getElementById = document.getElementById(hashFragment)) != null ? _document_getElementById : document.getElementsByName(hashFragment)[0];\n}\nclass InnerScrollAndFocusHandler extends _react.default.Component {\n    componentDidMount() {\n        this.handlePotentialScroll();\n    }\n    componentDidUpdate() {\n        // Because this property is overwritten in handlePotentialScroll it's fine to always run it when true as it'll be set to false for subsequent renders.\n        if (this.props.focusAndScrollRef.apply) {\n            this.handlePotentialScroll();\n        }\n    }\n    render() {\n        return this.props.children;\n    }\n    constructor(...args){\n        super(...args);\n        this.handlePotentialScroll = ()=>{\n            // Handle scroll and focus, it's only applied once in the first useEffect that triggers that changed.\n            const { focusAndScrollRef, segmentPath } = this.props;\n            if (focusAndScrollRef.apply) {\n                // segmentPaths is an array of segment paths that should be scrolled to\n                // if the current segment path is not in the array, the scroll is not applied\n                // unless the array is empty, in which case the scroll is always applied\n                if (focusAndScrollRef.segmentPaths.length !== 0 && !focusAndScrollRef.segmentPaths.some((scrollRefSegmentPath)=>segmentPath.every((segment, index)=>(0, _matchsegments.matchSegment)(segment, scrollRefSegmentPath[index])))) {\n                    return;\n                }\n                let domNode = null;\n                const hashFragment = focusAndScrollRef.hashFragment;\n                if (hashFragment) {\n                    domNode = getHashFragmentDomNode(hashFragment);\n                }\n                // `findDOMNode` is tricky because it returns just the first child if the component is a fragment.\n                // This already caused a bug where the first child was a <link/> in head.\n                if (!domNode) {\n                    domNode = findDOMNode(this);\n                }\n                // If there is no DOM node this layout-router level is skipped. It'll be handled higher-up in the tree.\n                if (!(domNode instanceof Element)) {\n                    return;\n                }\n                // Verify if the element is a HTMLElement and if we want to consider it for scroll behavior.\n                // If the element is skipped, try to select the next sibling and try again.\n                while(!(domNode instanceof HTMLElement) || shouldSkipElement(domNode)){\n                    // No siblings found that match the criteria are found, so handle scroll higher up in the tree instead.\n                    if (domNode.nextElementSibling === null) {\n                        return;\n                    }\n                    domNode = domNode.nextElementSibling;\n                }\n                // State is mutated to ensure that the focus and scroll is applied only once.\n                focusAndScrollRef.apply = false;\n                focusAndScrollRef.hashFragment = null;\n                focusAndScrollRef.segmentPaths = [];\n                (0, _handlesmoothscroll.handleSmoothScroll)(()=>{\n                    // In case of hash scroll, we only need to scroll the element into view\n                    if (hashFragment) {\n                        domNode.scrollIntoView();\n                        return;\n                    }\n                    // Store the current viewport height because reading `clientHeight` causes a reflow,\n                    // and it won't change during this function.\n                    const htmlElement = document.documentElement;\n                    const viewportHeight = htmlElement.clientHeight;\n                    // If the element's top edge is already in the viewport, exit early.\n                    if (topOfElementInViewport(domNode, viewportHeight)) {\n                        return;\n                    }\n                    // Otherwise, try scrolling go the top of the document to be backward compatible with pages\n                    // scrollIntoView() called on `<html/>` element scrolls horizontally on chrome and firefox (that shouldn't happen)\n                    // We could use it to scroll horizontally following RTL but that also seems to be broken - it will always scroll left\n                    // scrollLeft = 0 also seems to ignore RTL and manually checking for RTL is too much hassle so we will scroll just vertically\n                    htmlElement.scrollTop = 0;\n                    // Scroll to domNode if domNode is not in viewport when scrolled to top of document\n                    if (!topOfElementInViewport(domNode, viewportHeight)) {\n                        domNode.scrollIntoView();\n                    }\n                }, {\n                    // We will force layout by querying domNode position\n                    dontForceLayout: true,\n                    onlyHashChange: focusAndScrollRef.onlyHashChange\n                });\n                // Mutate after scrolling so that it can be read by `handleSmoothScroll`\n                focusAndScrollRef.onlyHashChange = false;\n                // Set focus on the element\n                domNode.focus();\n            }\n        };\n    }\n}\nfunction ScrollAndFocusHandler(param) {\n    let { segmentPath, children } = param;\n    const context = (0, _react.useContext)(_approutercontextsharedruntime.GlobalLayoutRouterContext);\n    if (!context) {\n        throw new Error(\"invariant global layout router not mounted\");\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(InnerScrollAndFocusHandler, {\n        segmentPath: segmentPath,\n        focusAndScrollRef: context.focusAndScrollRef,\n        children: children\n    });\n}\n_c = ScrollAndFocusHandler;\n/**\n * InnerLayoutRouter handles rendering the provided segment based on the cache.\n */ function InnerLayoutRouter(param) {\n    let { parallelRouterKey, url, childNodes, segmentPath, tree, // isActive,\n    cacheKey } = param;\n    const context = (0, _react.useContext)(_approutercontextsharedruntime.GlobalLayoutRouterContext);\n    if (!context) {\n        throw new Error(\"invariant global layout router not mounted\");\n    }\n    const { buildId, changeByServerResponse, tree: fullTree } = context;\n    // Read segment path from the parallel router cache node.\n    let childNode = childNodes.get(cacheKey);\n    // When data is not available during rendering client-side we need to fetch\n    // it from the server.\n    if (childNode === undefined) {\n        const newLazyCacheNode = {\n            lazyData: null,\n            rsc: null,\n            prefetchRsc: null,\n            head: null,\n            prefetchHead: null,\n            parallelRoutes: new Map(),\n            lazyDataResolved: false,\n            loading: null\n        };\n        /**\n     * Flight data fetch kicked off during render and put into the cache.\n     */ childNode = newLazyCacheNode;\n        childNodes.set(cacheKey, newLazyCacheNode);\n    }\n    // `rsc` represents the renderable node for this segment.\n    // If this segment has a `prefetchRsc`, it's the statically prefetched data.\n    // We should use that on initial render instead of `rsc`. Then we'll switch\n    // to `rsc` when the dynamic response streams in.\n    //\n    // If no prefetch data is available, then we go straight to rendering `rsc`.\n    const resolvedPrefetchRsc = childNode.prefetchRsc !== null ? childNode.prefetchRsc : childNode.rsc;\n    // We use `useDeferredValue` to handle switching between the prefetched and\n    // final values. The second argument is returned on initial render, then it\n    // re-renders with the first argument.\n    //\n    // @ts-expect-error The second argument to `useDeferredValue` is only\n    // available in the experimental builds. When its disabled, it will always\n    // return `rsc`.\n    const rsc = (0, _react.useDeferredValue)(childNode.rsc, resolvedPrefetchRsc);\n    // `rsc` is either a React node or a promise for a React node, except we\n    // special case `null` to represent that this segment's data is missing. If\n    // it's a promise, we need to unwrap it so we can determine whether or not the\n    // data is missing.\n    const resolvedRsc = typeof rsc === \"object\" && rsc !== null && typeof rsc.then === \"function\" ? (0, _react.use)(rsc) : rsc;\n    if (!resolvedRsc) {\n        // The data for this segment is not available, and there's no pending\n        // navigation that will be able to fulfill it. We need to fetch more from\n        // the server and patch the cache.\n        // Check if there's already a pending request.\n        let lazyData = childNode.lazyData;\n        if (lazyData === null) {\n            /**\n       * Router state with refetch marker added\n       */ // TODO-APP: remove ''\n            const refetchTree = walkAddRefetch([\n                \"\",\n                ...segmentPath\n            ], fullTree);\n            const includeNextUrl = (0, _hasinterceptionrouteincurrenttree.hasInterceptionRouteInCurrentTree)(fullTree);\n            childNode.lazyData = lazyData = (0, _fetchserverresponse.fetchServerResponse)(new URL(url, location.origin), refetchTree, includeNextUrl ? context.nextUrl : null, buildId);\n            childNode.lazyDataResolved = false;\n        }\n        /**\n     * Flight response data\n     */ // When the data has not resolved yet `use` will suspend here.\n        const serverResponse = (0, _react.use)(lazyData);\n        if (!childNode.lazyDataResolved) {\n            // setTimeout is used to start a new transition during render, this is an intentional hack around React.\n            setTimeout(()=>{\n                (0, _react.startTransition)(()=>{\n                    changeByServerResponse({\n                        previousTree: fullTree,\n                        serverResponse\n                    });\n                });\n            });\n            // It's important that we mark this as resolved, in case this branch is replayed, we don't want to continously re-apply\n            // the patch to the tree.\n            childNode.lazyDataResolved = true;\n        }\n        // Suspend infinitely as `changeByServerResponse` will cause a different part of the tree to be rendered.\n        // A falsey `resolvedRsc` indicates missing data -- we should not commit that branch, and we need to wait for the data to arrive.\n        (0, _react.use)(_unresolvedthenable.unresolvedThenable);\n    }\n    // If we get to this point, then we know we have something we can render.\n    const subtree = /*#__PURE__*/ (0, _jsxruntime.jsx)(_approutercontextsharedruntime.LayoutRouterContext.Provider, {\n        value: {\n            tree: tree[1][parallelRouterKey],\n            childNodes: childNode.parallelRoutes,\n            // TODO-APP: overriding of url for parallel routes\n            url: url,\n            loading: childNode.loading\n        },\n        children: resolvedRsc\n    });\n    // Ensure root layout is not wrapped in a div as the root layout renders `<html>`\n    return subtree;\n}\n_c1 = InnerLayoutRouter;\n/**\n * Renders suspense boundary with the provided \"loading\" property as the fallback.\n * If no loading property is provided it renders the children without a suspense boundary.\n */ function LoadingBoundary(param) {\n    let { children, hasLoading, loading, loadingStyles, loadingScripts } = param;\n    // We have an explicit prop for checking if `loading` is provided, to disambiguate between a loading\n    // component that returns `null` / `undefined`, vs not having a loading component at all.\n    if (hasLoading) {\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(_react.Suspense, {\n            fallback: /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n                children: [\n                    loadingStyles,\n                    loadingScripts,\n                    loading\n                ]\n            }),\n            children: children\n        });\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_jsxruntime.Fragment, {\n        children: children\n    });\n}\n_c2 = LoadingBoundary;\nfunction OuterLayoutRouter(param) {\n    let { parallelRouterKey, segmentPath, error, errorStyles, errorScripts, templateStyles, templateScripts, template, notFound, notFoundStyles, styles } = param;\n    const context = (0, _react.useContext)(_approutercontextsharedruntime.LayoutRouterContext);\n    if (!context) {\n        throw new Error(\"invariant expected layout router to be mounted\");\n    }\n    const { childNodes, tree, url, loading } = context;\n    // Get the current parallelRouter cache node\n    let childNodesForParallelRouter = childNodes.get(parallelRouterKey);\n    // If the parallel router cache node does not exist yet, create it.\n    // This writes to the cache when there is no item in the cache yet. It never *overwrites* existing cache items which is why it's safe in concurrent mode.\n    if (!childNodesForParallelRouter) {\n        childNodesForParallelRouter = new Map();\n        childNodes.set(parallelRouterKey, childNodesForParallelRouter);\n    }\n    // Get the active segment in the tree\n    // The reason arrays are used in the data format is that these are transferred from the server to the browser so it's optimized to save bytes.\n    const treeSegment = tree[1][parallelRouterKey][0];\n    // If segment is an array it's a dynamic route and we want to read the dynamic route value as the segment to get from the cache.\n    const currentChildSegmentValue = (0, _getsegmentvalue.getSegmentValue)(treeSegment);\n    /**\n   * Decides which segments to keep rendering, all segments that are not active will be wrapped in `<Offscreen>`.\n   */ // TODO-APP: Add handling of `<Offscreen>` when it's available.\n    const preservedSegments = [\n        treeSegment\n    ];\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n        children: [\n            styles,\n            preservedSegments.map((preservedSegment)=>{\n                const preservedSegmentValue = (0, _getsegmentvalue.getSegmentValue)(preservedSegment);\n                const cacheKey = (0, _createroutercachekey.createRouterCacheKey)(preservedSegment);\n                return(/*\n            - Error boundary\n              - Only renders error boundary if error component is provided.\n              - Rendered for each segment to ensure they have their own error state.\n            - Loading boundary\n              - Only renders suspense boundary if loading components is provided.\n              - Rendered for each segment to ensure they have their own loading state.\n              - Passed to the router during rendering to ensure it can be immediately rendered when suspending on a Flight fetch.\n          */ /*#__PURE__*/ (0, _jsxruntime.jsxs)(_approutercontextsharedruntime.TemplateContext.Provider, {\n                    value: /*#__PURE__*/ (0, _jsxruntime.jsx)(ScrollAndFocusHandler, {\n                        segmentPath: segmentPath,\n                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_errorboundary.ErrorBoundary, {\n                            errorComponent: error,\n                            errorStyles: errorStyles,\n                            errorScripts: errorScripts,\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(LoadingBoundary, {\n                                hasLoading: Boolean(loading),\n                                loading: loading == null ? void 0 : loading[0],\n                                loadingStyles: loading == null ? void 0 : loading[1],\n                                loadingScripts: loading == null ? void 0 : loading[2],\n                                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_notfoundboundary.NotFoundBoundary, {\n                                    notFound: notFound,\n                                    notFoundStyles: notFoundStyles,\n                                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_redirectboundary.RedirectBoundary, {\n                                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(InnerLayoutRouter, {\n                                            parallelRouterKey: parallelRouterKey,\n                                            url: url,\n                                            tree: tree,\n                                            childNodes: childNodesForParallelRouter,\n                                            segmentPath: segmentPath,\n                                            cacheKey: cacheKey,\n                                            isActive: currentChildSegmentValue === preservedSegmentValue\n                                        })\n                                    })\n                                })\n                            })\n                        })\n                    }),\n                    children: [\n                        templateStyles,\n                        templateScripts,\n                        template\n                    ]\n                }, (0, _createroutercachekey.createRouterCacheKey)(preservedSegment, true)));\n            })\n        ]\n    });\n}\n_c3 = OuterLayoutRouter;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=layout-router.js.map\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"ScrollAndFocusHandler\");\n$RefreshReg$(_c1, \"InnerLayoutRouter\");\n$RefreshReg$(_c2, \"LoadingBoundary\");\n$RefreshReg$(_c3, \"OuterLayoutRouter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2xheW91dC1yb3V0ZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7eURBMEJPQSxDQUFBLENBQUFDLG1CQUFBQSxDQUFBO3VDQUM2QkEsbUJBQUFBLENBQUE7aURBQ0Q7Z0RBQ0w7MkNBQ0Q7O2dEQUVJOzhDQUNBOzhDQUNEOzs7QUFJaEMsTUFBQUMscUNBQUFELG1CQUFBQSxDQUFBOzs7O0lBUUUsU0FBSUUsZUFBbUJBLGlCQUFBLEVBQUFDLGNBQUE7UUFDckJELG1CQUFnQkU7UUFDaEIsTUFBTUMsQ0FBQUEsU0FBU0gsaUJBQWtCSSxHQUFBQTtRQUVqQyxNQUFJQyxTQUFBQSxrQkFBQUEsTUFBYUosS0FBQUE7WUFDZixJQUFJQSxlQUFlSSxZQUFHQyxFQUFBQSxjQUFlSixDQUFBQSxFQUFBQSxFQUFBQSxVQUFtQjtnQkFDdERELGNBQVksSUFBQUssY0FBQSxDQUFBSixtQkFBQTtvQkFDVkMsUUFBTUk7b0JBSU4sTUFBQUEsVUFBT0MsZUFBQUMsV0FBQVIsY0FBQSxJQUFBQyxpQkFBQTsyQkFDTEQ7d0JBQ0FBLGNBQUE7OzRCQUVFLEdBQUNDLGNBQUFBLENBQWlCLEVBQUU7OENBQ1I7Z0NBQ1ZLLE9BQU8sQ0FBQyxFQUFFO2dDQUNWQSxPQUFPLENBQUMsRUFBRTtnQ0FDVkEsT0FBQTtnQ0FDRDs2QkFDSDt3QkFDRDtxQkFDSDtnQkFFQTt1QkFDRU47b0JBQ0FBLGNBQUE7O3dCQUVFLEdBQUNDLGNBQUFBLENBQWlCLEVBQUVNO3dCQUl0QixDQUFBTixpQkFBQSxFQUFBTSxlQUFBUixrQkFBQVUsS0FBQSxLQUFBVCxjQUFBLElBQUFDLGlCQUFBO29CQUNEO2lCQUNIO1lBQ0Y7UUFDRjtJQUVBO0lBQ0YsT0FBQUQ7QUFFQTtBQUNBOzs7SUFNRSxTQUFBVSxZQUFBQyxRQUFBO0lBQ0EsK0JBQXNCO0lBQ3RCLFdBQUFDLFdBQUE7SUFDQSx3REFBMkM7UUFDekNDLElBQU1DLEVBQStCQztRQUNyQyxNQUFJRCx1QkFBQUUsUUFBQUQsS0FBQTtZQUNGQzs7b0JBQW9CQyxJQUFBQSxPQUFBQSxVQUFBQSxNQUFBQSxFQUFBQSxXQUFBQSxJQUFBQSxNQUFBQSxPQUFBQSxPQUFBQSxHQUFBQSxPQUFBQSxNQUFBQSxPQUFBQTs7Z0JBQ2xCO2dCQUNBLDREQUEwQjtvQkFDeEJILENBQUFBLFFBQUFBLENBQUFBLEVBQUFBLENBQUFBLFFBQUFBLENBQUFBLDZDQUF3Qkc7b0JBQzFCSCx3QkFBQUc7Z0JBQ0Y7WUFDQTtZQUNGLE9BQVVDLFVBQUFDLE9BQUEsQ0FBQVQsV0FBQSxDQUFBQztpQkFDUks7WUFDRkEsUUFBQUQsS0FBQSxHQUFBRDtRQUNGO0lBQ0E7SUFDRixPQUFBSSxVQUFBQyxPQUFBLENBQUFULFdBQUEsQ0FBQUM7QUFFQTtNQUNFUyxpQkFBQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0Q7Q0FDRDs7O0lBSUUsU0FBQUMsa0JBQUFDLE9BQUE7SUFDQSxrR0FBMEY7SUFDMUYsMEZBQW1EO0lBQ25ELG1EQUFJO1FBQUM7UUFBVTtRQUFTQztjQUNsQlYsQ0FBQUEsaUJBQW9CUyxTQUFLRSxRQUFBLEdBQWU7WUFDMUNSLElBQ0U7WUFHSkEsUUFBQVMsSUFBQSw2RkFBQUg7UUFDQTtRQUNGO0lBRUE7SUFDQSwyRkFBd0Q7SUFDeEQsd0RBQTBDO0lBQzFDLE1BQUFJLE9BQU9OLFFBQUFBLHFCQUFvQ087SUFDN0MsT0FBQVAsZUFBQVEsS0FBQSxFQUFBRCxPQUFBRCxJQUFBLENBQUFDLEtBQUE7QUFFQTs7O0lBSUUsU0FBTUQsdUJBQWVHLE9BQUFBLEVBQUFBLGNBQXFCO0lBQzFDLE1BQUFILE9BQVlJLFFBQU9ELHFCQUFpQkU7SUFDdEMsT0FBQUwsS0FBQUksR0FBQSxTQUFBSixLQUFBSSxHQUFBLElBQUFDO0FBRUE7Ozs7OztJQU9FLFNBQUFDLHVCQUFBQyxZQUFBO0lBQ0EsK0VBQTRCO1FBQzFCQSxpQkFBZ0JDLE9BQUk7UUFDdEIsT0FBQUMsU0FBQUQsSUFBQTs7SUFFQSxJQUFBRTtJQUNBLHFGQUNFRDtJQUVBQSxPQUFBQSxDQUFBQSwyQkFBMkJGLFNBQWdCSSxjQUFBLENBQUFKLGFBQUEsWUFBQUcsMkJBRS9DRCxTQUFBRyxpQkFBQSxDQUFBTCxhQUFBO0FBTUE7TUFvR0VNLG1DQUFvQkMsT0FBQXJCLE9BQUEsQ0FBQXNCLFNBQUE7d0JBQ2JDO1FBQ1AsS0FBQUEscUJBQUE7SUFFQUM7eUJBQ0U7UUFDQSxzSkFBd0M7WUFDdEMsSUFBSSxDQUFDRCxLQUFBQSxDQUFBQSxpQkFBcUIsQ0FBQUUsS0FBQTtZQUM1QixLQUFBRixxQkFBQTtRQUNGO0lBRUFHO2FBQ0U7UUFDRixZQUFBQyxLQUFBLENBQUFDLFFBQUE7OzthQWhIQUwsSUFBQUE7WUFDRSxDQUFBQSxxQkFBQTtZQUNBLHFHQUFxRDtZQUVyRCxNQUFJTSxFQUFBQSxpQkFBa0JKLEVBQUtLLFdBQUUsVUFBQUgsS0FBQTtnQkFDM0JFLGtCQUFBSixLQUFBO2dCQUNBO2dCQUNBLDZFQUF3RTtnQkFDeEUsd0VBRXFCTTtvQkFNbkJGLGtCQUFBRSxZQUFBLENBQUEvQyxNQUFBLFdBQUE2QyxrQkFBQUUsWUFBQSxDQUFBQyxJQUFBLEVBQUFDLHVCQUFBSCxZQUFBckIsS0FBQSxFQUFBeUIsU0FBQUMsUUFBQSxJQUFBQyxlQUFBbkQsWUFBQSxFQUFBaUQsU0FBQUQsb0JBQUEsQ0FBQUUsTUFBQTtvQkFDRjtnQkFFQTtnQkFHQSxJQUFBRSxVQUFNdkI7Z0JBRU4sTUFBSUEsZUFBY2Usa0JBQUFmLFlBQUE7b0JBQ2hCdUIsY0FBVXhCO29CQUNad0IsVUFBQXhCLHVCQUFBQztnQkFFQTtnQkFDQSxrR0FBeUU7Z0JBQ3pFLHlFQUFjO29CQUNadUIsQ0FBQUEsU0FBVTlDO29CQUNaOEMsVUFBQTlDLFlBQUE7Z0JBRUE7Z0JBQ0EsdUdBQW1DO29CQUNqQyxDQUFBOEMsQ0FBQUEsbUJBQUFDLE9BQUE7b0JBQ0Y7Z0JBRUE7Z0JBQ0EsNEZBQTJFO2dCQUMzRSwyRUFBd0U7c0JBQ3RFLENBQUFELENBQUFBLG1CQUFBRSxXQUFBLEtBQUFyQyxrQkFBQW1DLFNBQUE7b0JBQ0EsdUdBQXlDO3dCQUN2Q0EsUUFBQUcsa0JBQUE7d0JBQ0Y7b0JBQ0FIO29CQUNGQSxVQUFBQSxRQUFBRyxrQkFBQTtnQkFFQTtnQkFDQVgsNkVBQTBCO2dCQUMxQkEsa0JBQWtCZixLQUFBQSxHQUFBQTtnQkFDbEJlLGtCQUFrQkUsWUFBWSxHQUFHO2dCQUVqQ1Usa0JBQUFBLFlBQUFBLEdBQUFBLEVBQUFBO29CQUVJQyxvQkFBQUQsa0JBQUE7b0JBQ0EsdUVBQWtCO3dCQUNkSixjQUF3Qk07d0JBRTFCTixRQUFBTSxjQUFBO3dCQUNGO29CQUNBO29CQUNBLG9GQUE0QztvQkFDNUMsNENBQTRDO29CQUM1QyxNQUFNL0IsY0FBQUEsU0FBaUJnQyxlQUFZQztvQkFFbkMsTUFBQWpDLGlCQUFBZ0MsWUFBQUMsWUFBQTtvQkFDQSxvRUFBb0U7d0JBQ2xFQyx1QkFBQVQsU0FBQXpCLGlCQUFBO3dCQUNGO29CQUVBO29CQUNBO29CQUNBO29CQUNBO29CQUNBZ0MsNkhBQXdCO29CQUV4QkEsWUFBQUcsU0FBQTtvQkFDQSxtRkFBcUU7d0JBRWpFVixDQUFBQSx1QkFBc0NBLFNBQUF6QixpQkFBQTt3QkFDMUN5QixRQUFBTSxjQUFBO29CQUVGOztvQkFFRUssb0RBQWlCO29CQUNqQkMsaUJBQWdCcEI7b0JBQ2xCb0IsZ0JBQUFwQixrQkFBQW9CLGNBQUE7Z0JBR0Y7Z0JBQ0FwQix3RUFBbUM7Z0JBRW5DQSxrQkFBQW9CLGNBQTJCO2dCQUMzQlosMkJBQWE7Z0JBQ2ZBLFFBQUFhLEtBQUE7WUFDRjs7SUFnQkY7QUFFQTtTQUErQkMsc0JBRXJCQyxLQUZxQjtJQU83QixNQUFNQyxXQUFVQyxFQUFBQSxRQUFBQSxFQUFBQSxHQUFBQTtJQUNoQixNQUFLRCxVQUFTLElBQUFoQyxPQUFBaUMsVUFBQSxFQUFBQywrQkFBQUMseUJBQUE7UUFDWixDQUFBSCxTQUFVSTtRQUNaLFVBQUFBLE1BQUE7SUFFQTtXQUVpQjNCLFdBQUFBLEdBQUFBLENBQUFBLEdBQUFBLFlBQUFBLEdBQUFBLEVBQUFBLDRCQUFBQTtRQUNiRCxhQUFBQTsyQkFFQ0QsUUFBQUEsaUJBQUFBOztJQUdQO0FBRUE7S0F0QitCdUI7OztJQXlCSixTQUN6Qk8sa0JBQ0FDLEtBQ0FDO0lBSUEsTUFBQUYsaUJBQVksRUFBQUMsR0FBQSxFQUFBQyxVQUFBLEVBQUE5QixXQUFBLEVBQUErQixJQUFBLEVBQ1pDLFlBU0Q7SUFDQ0EsUUFBTVQsRUFBQUEsR0FBQUE7SUFDTixNQUFLQSxVQUFTLElBQUFoQyxPQUFBaUMsVUFBQSxFQUFBQywrQkFBQUMseUJBQUE7UUFDWixDQUFBSCxTQUFVSTtRQUNaLFVBQUFBLE1BQUE7SUFFQTtJQUVBLFFBQUFNLE9BQUEsRUFBQUMsc0JBQUEsRUFBQUgsTUFBQUksUUFBQSxFQUF5RCxHQUFBWjtJQUN6RCx5REFBK0JTO0lBRS9CLElBQUFJLFlBQUFOLFdBQUFPLEdBQUEsQ0FBQUw7SUFDQSwyRUFBc0I7SUFDdEIsc0JBQWtCekU7UUFDaEI2RSxjQUFNRSxXQUFrQztjQUN0Q0MsbUJBQVU7WUFDVkMsVUFBSztZQUNMQyxLQUFBQTtZQUNBQyxhQUFNO1lBQ05DLE1BQUFBO1lBQ0FDLGNBQUFBO1lBQ0FDLGdCQUFBQSxJQUFrQkM7WUFDbEJDLGtCQUFTO1lBQ1hBLFNBQUE7UUFFQTs7O1FBSUFqQixZQUFXa0I7UUFDYmxCLFdBQUFrQixHQUFBLENBQUFoQixVQUFBTTtJQUVBO0lBRUE7SUFDQSw0RUFBMkU7SUFDM0UsMkVBQWlEO0lBQ2pELGlEQUFFO0lBQ0Y7SUFDQSw0RUFDNkNHO0lBRTdDLE1BQUFRLHNCQUFBYixVQUFBSyxXQUFBLFlBQUFMLFVBQUFLLFdBQTJFLEdBQUFMLFVBQUFJLEdBQUE7SUFDM0UsMkVBQTJFO0lBQzNFLDJFQUFzQztJQUN0QyxzQ0FBRTtJQUNGO0lBQ0E7SUFDQSwwRUFBZ0I7SUFDaEIsZ0JBQWlCVTtJQUVqQixNQUFBVixNQUFBLElBQUFqRCxPQUFBMkQsZ0JBQUEsRUFBQWQsVUFBQUksR0FBQSxFQUFBUztJQUNBO0lBQ0E7SUFDQSw4RUFBbUI7SUFDbkIsbUJBQU1FO0lBS04sTUFBS0EsY0FBYSxPQUFBWCxRQUFBLFlBQUFBLFFBQUEsZUFBQUEsSUFBQVksSUFBQSxzQkFBQTdELE9BQUE4RCxHQUFBLEVBQUFiLE9BQUFBO1FBQ2hCLENBQUFXLGFBQUE7UUFDQTtRQUNBLHlFQUFrQztRQUVsQztRQUNBLDhDQUFpQztRQUNqQyxJQUFJWixXQUFBQSxVQUFtQkEsUUFBQTtZQUNyQkEsYUFBQTs7O2dDQUlvQmpGO2tCQUFnQmdHLGNBQUFoRyxlQUFBOzttQkFBcUI2RTthQUN6RCxFQUFBQTtZQUNBQyxNQUFBQSxpQkFBcUJHLENBQUFBLEdBQUFBLG1DQUFXZ0IsaUNBQ2pCQyxFQUFBQTtZQUtmcEIsVUFBVVMsUUFBQUEsR0FBQUEsV0FBbUIsSUFBQVkscUJBQUFGLG1CQUFBLE1BQUFHLElBQUE3QixLQUFBMkIsU0FBQUcsTUFBQSxHQUFBTCxhQUFBTSxpQkFBQXJDLFFBQUFzQyxPQUFBLFNBQUE1QjtZQUMvQkcsVUFBQVMsZ0JBQUE7UUFFQTs7O1FBSUEsOERBQTJCTjtRQUUzQixNQUFLSCxpQkFBVVMsQ0FBQUEsR0FBQUEsT0FBa0JRLEdBQUEsRUFBQWQ7WUFDL0IsQ0FBQUgsVUFBQVMsZ0JBQUE7WUFDQWlCLHdHQUFXO3VCQUNUQztvQkFDRTdCLE9BQUFBLGVBQUFBLEVBQXVCOzJDQUNQQzt3QkFDZDZCLGNBQUFBO3dCQUNGQTtvQkFDRjtnQkFDRjtZQUVBO1lBQ0EsdUhBQXlCO1lBQ3pCNUIseUJBQVVTO1lBQ1pULFVBQUFTLGdCQUFBO1FBQ0E7UUFDQTtRQUNBUSxpSUFBc0I7UUFDeEIsSUFBQTlELE9BQUE4RCxHQUFBLEVBQUFZLG9CQUFBQyxrQkFBQTtJQUVBO0lBQ0EseUVBQ0U7b0JBRVMsa0JBQUFDLFlBQUFDLEdBQUEsRUFBQTNDLCtCQUFBNEMsbUJBQUEsQ0FBQUMsUUFBQTtlQUNMdkM7WUFDQUQsTUFBQUEsSUFBQUEsQ0FBQUEsRUFBWU0sQ0FBQUEsa0JBQVVRO1lBQ3RCZCxZQUFBTSxVQUFBUSxjQUFBO1lBQ0FmLGtEQUFLQTtZQUNMa0IsS0FBQUE7WUFDRkEsU0FBQVgsVUFBQVcsT0FBQTs7O0lBS0o7SUFDQSxpRkFBT3dCO0lBQ1QsT0FBQUE7QUFFQTtNQWhKRTNDOzs7O0lBb0p1QixTQUN2QjlCLGdCQUNBMEUsS0FDQXpCO0lBVUEsTUFBQWpELFFBQUEsRUFBQTBFLFVBQUEsRUFBQXpCLE9BQUEsRUFBQTBCLGFBQUEsRUFBQUMsY0FBQSxLQUFBcEQ7SUFDQSxvR0FBeUY7SUFDekYseUZBQWdCO1FBQ2RrRCxZQUFBO2VBRUlHLFdBQUFBLEdBQUFBLENBQUFBLEdBQUFBLFlBQ0VQLEdBQUEsRUFBQTdFLE9BQUFxRixRQUFBOzswQkFDR0g7b0JBQ0FDO29CQUNBM0I7Ozs7O1FBT1g7SUFFQTtXQUFVakQsV0FBQUEsR0FBQUEsQ0FBQUEsR0FBQUEsWUFBQUEsR0FBQUEsRUFBQUEsWUFBQUEsUUFBQUEsRUFBQUE7O0lBQ1o7QUFNZTtNQXJDYkE7U0FxQ3dDK0Usa0JBQ3ZCdkQsS0FDakJ0QjtJQXVCQSxNQUFNdUIsaUJBQVVDLEVBQUFBLFdBQUFBLEVBQVUxRCxLQUFDdUcsRUFBQUEsV0FBQUEsRUFBQUEsWUFBQUEsRUFBQUEsY0FBQUEsRUFBQUEsZUFBbUIsRUFBQVMsUUFBQSxFQUFBQyxRQUFBLEVBQUFDLGNBQUEsRUFBQUMsTUFBQSxLQUFBM0Q7SUFDOUMsTUFBS0MsVUFBUyxJQUFBaEMsT0FBQWlDLFVBQUEsRUFBQUMsK0JBQUE0QyxtQkFBQTtRQUNaLENBQUE5QyxTQUFVSTtRQUNaLFVBQUFBLE1BQUE7SUFFQTtJQUVBLFFBQUFHLFVBQUEsRUFBQUMsSUFBQSxFQUFBRixHQUFBLEVBQUFrQixPQUFBLEtBQUF4QjtJQUNBLDRDQUFrQ087SUFDbEMsSUFBQW9ELDhCQUFBcEQsV0FBQU8sR0FBQSxDQUFBVDtJQUNBO0lBQ0EseUpBQWtDO1FBQ2hDc0QsQ0FBQUEsNkJBQThCO1FBQzlCcEQsOEJBQWVGLElBQW1Cc0Q7UUFDcENwRCxXQUFBa0IsR0FBQSxDQUFBcEIsbUJBQUFzRDtJQUVBO0lBQ0E7SUFDQSw4SUFBaUQ7SUFFakQsTUFBQUMsY0FBQXBELElBQUEsSUFBQUgsa0JBQUE7SUFDQSxnSUFBaUR1RDtJQUVqRCxNQUFBQywyQkFBQSxJQUFBQyxpQkFBQUMsZUFBQSxFQUFBSDs7O01BSUEsK0RBQXFDO1VBQUNBLG9CQUFBQTtRQUFZQTtLQUVsRDs7a0JBRUtGO1lBQ0FNOzhCQUNPQyxHQUFBQSxDQUFBQSxDQUFBQTtnQkFDTixNQUFNeEQsd0JBQVd5RCxDQUFBQSxHQUFBQSxpQkFBQUEsZUFBcUJDLEVBQUFBO2dCQUV0QyxNQUFBMUQsV0FDRSxJQUFBMkQsc0JBQUFGLG9CQUFBLEVBQUFDO3VCQVdFRTs7Ozs7Ozs7d0JBQUFBLEdBQUFBLENBQUFBLEdBQUFBLFlBQ0VDLElBQUEsRUFBQXBFLCtCQUFDSixlQUFBQSxDQUFBQSxRQUFBQSxFQUFBQTsyQkFBbUNyQixXQUFBQSxHQUFBQSxDQUFBQSxHQUFBQSxZQUFBQSxHQUFBQSxFQUFBQSx1QkFBQUE7cUNBQ2xDQTtrQ0FDa0JsQyxXQUFBQSxHQUFBQSxDQUFBQSxHQUFBQSxZQUFBQSxHQUFBQSxFQUFBQSxlQUFBQSxhQUFBQSxFQUFBQTs0QkFDaEJpSSxnQkFBYUE7NEJBQ2JDLGFBQUFBOzBDQUVBQTtzQ0FDc0JqRCxXQUFSa0QsR0FBUWxELENBQUFBLEdBQUFBLFlBQUFBLEdBQUFBLEVBQUFBLGlCQUFBQTtnQ0FDcEJBLFlBQVNBLFFBQUFBO2dDQUNUMEIsU0FBQUEsV0FBZTFCLE9BQUFBLEtBQUFBLElBQUFBLE9BQUFBLENBQUFBLEVBQUFBO2dDQUNmMkIsZUFBYzNCLFdBQUVBLE9BQUFBLEtBQUFBLElBQUFBLE9BQUFBLENBQVMsRUFBQztnREFFMUJBLFdBQUEsZ0JBQUFBLE9BQUNtRCxDQUFBQSxFQUFBQTswQ0FDV25CLFdBQUFBLEdBQUFBLENBQUFBLEdBQUFBLFlBQUFBLEdBQUFBLEVBQUFBLGtCQUFBQSxnQkFBQUEsRUFBQUE7b0NBQ1ZDLFVBQUFBO29EQUVBQTs4Q0FDRSxrQkFBQWIsWUFBQUMsR0FBQSxFQUFBK0Isa0JBQUNDLGdCQUFBQSxFQUFBQTtrREFDb0J4RSxXQUFuQkEsR0FBbUJBLENBQUFBLEdBQUFBLFlBQUFBLEdBQUFBLEVBQUFBLG1CQUFBQTs0Q0FDbkJDLG1CQUFLQTs0Q0FDTEUsS0FBQUE7NENBQ0FELE1BQUFBOzRDQUNBOUIsWUFBQUE7NENBQ0FnQyxhQUFVQTs0Q0FDVnFFLFVBQ0VqQjs7Ozs7Ozs7OEJBVWZrQjt3QkFDQUM7d0JBQ0F6Qjs7cUJBdkNJVztnQkEwQ1gsT0FBQUUsc0JBQUFGLG9CQUFBLEVBQUFDLGtCQUFBOzs7SUFHTjs7TUFySDBDYiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vLi4vc3JjL2NsaWVudC9jb21wb25lbnRzL2xheW91dC1yb3V0ZXIudHN4P2M2OWQiXSwibmFtZXMiOlsiXyIsInJlcXVpcmUiLCJfaGFzaW50ZXJjZXB0aW9ucm91dGVpbmN1cnJlbnR0cmVlIiwic2VnbWVudFBhdGhUb1dhbGsiLCJ0cmVlVG9SZWNyZWF0ZSIsInBhcmFsbGVsUm91dGVLZXkiLCJpc0xhc3QiLCJsZW5ndGgiLCJtYXRjaFNlZ21lbnQiLCJoYXNPd25Qcm9wZXJ0eSIsInN1YlRyZWUiLCJ3YWxrQWRkUmVmZXRjaCIsInVuZGVmaW5lZCIsInNsaWNlIiwiZmluZERPTU5vZGUiLCJpbnN0YW5jZSIsIndpbmRvdyIsInByb2Nlc3MiLCJvcmlnaW5hbENvbnNvbGVFcnJvciIsImVycm9yIiwiY29uc29sZSIsIm1lc3NhZ2VzIiwiX3JlYWN0ZG9tIiwiZGVmYXVsdCIsInJlY3RQcm9wZXJ0aWVzIiwic2hvdWxkU2tpcEVsZW1lbnQiLCJlbGVtZW50IiwiaW5jbHVkZXMiLCJwb3NpdGlvbiIsIndhcm4iLCJyZWN0IiwiaXRlbSIsImV2ZXJ5IiwiZ2V0Qm91bmRpbmdDbGllbnRSZWN0IiwidG9wIiwidmlld3BvcnRIZWlnaHQiLCJnZXRIYXNoRnJhZ21lbnREb21Ob2RlIiwiaGFzaEZyYWdtZW50IiwiYm9keSIsImRvY3VtZW50IiwiX2RvY3VtZW50X2dldEVsZW1lbnRCeUlkIiwiZ2V0RWxlbWVudEJ5SWQiLCJnZXRFbGVtZW50c0J5TmFtZSIsImNvbXBvbmVudERpZE1vdW50IiwiX3JlYWN0IiwiQ29tcG9uZW50IiwiaGFuZGxlUG90ZW50aWFsU2Nyb2xsIiwiY29tcG9uZW50RGlkVXBkYXRlIiwiYXBwbHkiLCJyZW5kZXIiLCJwcm9wcyIsImNoaWxkcmVuIiwiZm9jdXNBbmRTY3JvbGxSZWYiLCJzZWdtZW50UGF0aCIsInNlZ21lbnRQYXRocyIsInNvbWUiLCJzY3JvbGxSZWZTZWdtZW50UGF0aCIsInNlZ21lbnQiLCJpbmRleCIsIl9tYXRjaHNlZ21lbnRzIiwiZG9tTm9kZSIsIkVsZW1lbnQiLCJIVE1MRWxlbWVudCIsIm5leHRFbGVtZW50U2libGluZyIsImhhbmRsZVNtb290aFNjcm9sbCIsIl9oYW5kbGVzbW9vdGhzY3JvbGwiLCJzY3JvbGxJbnRvVmlldyIsImh0bWxFbGVtZW50IiwiY2xpZW50SGVpZ2h0IiwidG9wT2ZFbGVtZW50SW5WaWV3cG9ydCIsInNjcm9sbFRvcCIsImRvbnRGb3JjZUxheW91dCIsIm9ubHlIYXNoQ2hhbmdlIiwiZm9jdXMiLCJTY3JvbGxBbmRGb2N1c0hhbmRsZXIiLCJwYXJhbSIsImNvbnRleHQiLCJ1c2VDb250ZXh0IiwiX2FwcHJvdXRlcmNvbnRleHRzaGFyZWRydW50aW1lIiwiR2xvYmFsTGF5b3V0Um91dGVyQ29udGV4dCIsIkVycm9yIiwicGFyYWxsZWxSb3V0ZXJLZXkiLCJ1cmwiLCJjaGlsZE5vZGVzIiwidHJlZSIsImNhY2hlS2V5IiwiYnVpbGRJZCIsImNoYW5nZUJ5U2VydmVyUmVzcG9uc2UiLCJmdWxsVHJlZSIsImNoaWxkTm9kZSIsImdldCIsIm5ld0xhenlDYWNoZU5vZGUiLCJsYXp5RGF0YSIsInJzYyIsInByZWZldGNoUnNjIiwiaGVhZCIsInByZWZldGNoSGVhZCIsInBhcmFsbGVsUm91dGVzIiwibGF6eURhdGFSZXNvbHZlZCIsIk1hcCIsImxvYWRpbmciLCJzZXQiLCJyZXNvbHZlZFByZWZldGNoUnNjIiwidXNlRGVmZXJyZWRWYWx1ZSIsInJlc29sdmVkUnNjIiwidGhlbiIsInVzZSIsInJlZmV0Y2hUcmVlIiwiZmV0Y2hTZXJ2ZXJSZXNwb25zZSIsImxvY2F0aW9uIiwiX2ZldGNoc2VydmVycmVzcG9uc2UiLCJVUkwiLCJvcmlnaW4iLCJpbmNsdWRlTmV4dFVybCIsIm5leHRVcmwiLCJzZXRUaW1lb3V0Iiwic3RhcnRUcmFuc2l0aW9uIiwic2VydmVyUmVzcG9uc2UiLCJfdW5yZXNvbHZlZHRoZW5hYmxlIiwidW5yZXNvbHZlZFRoZW5hYmxlIiwiX2pzeHJ1bnRpbWUiLCJqc3giLCJMYXlvdXRSb3V0ZXJDb250ZXh0IiwiUHJvdmlkZXIiLCJzdWJ0cmVlIiwiaGFzTG9hZGluZyIsImxvYWRpbmdTdHlsZXMiLCJsb2FkaW5nU2NyaXB0cyIsImZhbGxiYWNrIiwiU3VzcGVuc2UiLCJPdXRlckxheW91dFJvdXRlciIsInRlbXBsYXRlIiwibm90Rm91bmQiLCJub3RGb3VuZFN0eWxlcyIsInN0eWxlcyIsImNoaWxkTm9kZXNGb3JQYXJhbGxlbFJvdXRlciIsInRyZWVTZWdtZW50IiwiY3VycmVudENoaWxkU2VnbWVudFZhbHVlIiwiX2dldHNlZ21lbnR2YWx1ZSIsImdldFNlZ21lbnRWYWx1ZSIsInByZXNlcnZlZFNlZ21lbnRzIiwicHJlc2VydmVkU2VnbWVudFZhbHVlIiwiY3JlYXRlUm91dGVyQ2FjaGVLZXkiLCJwcmVzZXJ2ZWRTZWdtZW50IiwiX2NyZWF0ZXJvdXRlcmNhY2hla2V5IiwidmFsdWUiLCJqc3hzIiwiZXJyb3JDb21wb25lbnQiLCJlcnJvclN0eWxlcyIsImVycm9yU2NyaXB0cyIsIkJvb2xlYW4iLCJOb3RGb3VuZEJvdW5kYXJ5IiwiX3JlZGlyZWN0Ym91bmRhcnkiLCJJbm5lckxheW91dFJvdXRlciIsImlzQWN0aXZlIiwidGVtcGxhdGVTdHlsZXMiLCJ0ZW1wbGF0ZVNjcmlwdHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/next/dist/client/components/layout-router.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/next/dist/client/components/render-from-template-context.js":
/*!***********************************************************************************!*\
  !*** ../node_modules/next/dist/client/components/render-from-template-context.js ***!
  \***********************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return RenderFromTemplateContext;\n    }\n}));\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/../node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/index.js\"));\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/../node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nfunction RenderFromTemplateContext() {\n    const children = (0, _react.useContext)(_approutercontextsharedruntime.TemplateContext);\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_jsxruntime.Fragment, {\n        children: children\n    });\n}\n_c = RenderFromTemplateContext;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=render-from-template-context.js.map\nvar _c;\n$RefreshReg$(_c, \"RenderFromTemplateContext\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3JlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O3VEQUdnQ0EsQ0FBQSxDQUFBQyxtQkFBQUEsQ0FBQTtBQUVqQixNQUFBQyxpQ0FBU0MsbUJBQUFBLENBQUFBLHlKQUFBQTtTQUN0QkE7SUFDQSxNQUFBQyxXQUFBLElBQU9DLE9BQUFDLFVBQUEsRUFBQUosK0JBQUFLLGVBQUE7V0FBR0gsV0FBQUEsR0FBQUEsQ0FBQUEsR0FBQUEsWUFBQUEsR0FBQUEsRUFBQUEsWUFBQUEsUUFBQUEsRUFBQUE7O0lBQ1o7O0tBRkVEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi8uLi9zcmMvY2xpZW50L2NvbXBvbmVudHMvcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC50c3g/OWFjMiJdLCJuYW1lcyI6WyJfIiwicmVxdWlyZSIsIl9hcHByb3V0ZXJjb250ZXh0c2hhcmVkcnVudGltZSIsIlJlbmRlckZyb21UZW1wbGF0ZUNvbnRleHQiLCJjaGlsZHJlbiIsIl9yZWFjdCIsInVzZUNvbnRleHQiLCJUZW1wbGF0ZUNvbnRleHQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/next/dist/client/components/render-from-template-context.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/next/dist/client/components/search-params.js":
/*!********************************************************************!*\
  !*** ../node_modules/next/dist/client/components/search-params.js ***!
  \********************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    createDynamicallyTrackedSearchParams: function() {\n        return createDynamicallyTrackedSearchParams;\n    },\n    createUntrackedSearchParams: function() {\n        return createUntrackedSearchParams;\n    }\n});\nconst _staticgenerationasyncstorageexternal = __webpack_require__(/*! ./static-generation-async-storage.external */ \"(shared)/../node_modules/next/dist/client/components/static-generation-async-storage.external.js\");\nconst _dynamicrendering = __webpack_require__(/*! ../../server/app-render/dynamic-rendering */ \"(app-pages-browser)/../node_modules/next/dist/server/app-render/dynamic-rendering.js\");\nconst _reflect = __webpack_require__(/*! ../../server/web/spec-extension/adapters/reflect */ \"(app-pages-browser)/../node_modules/next/dist/server/web/spec-extension/adapters/reflect.js\");\nfunction createUntrackedSearchParams(searchParams) {\n    const store = _staticgenerationasyncstorageexternal.staticGenerationAsyncStorage.getStore();\n    if (store && store.forceStatic) {\n        return {};\n    } else {\n        return searchParams;\n    }\n}\nfunction createDynamicallyTrackedSearchParams(searchParams) {\n    const store = _staticgenerationasyncstorageexternal.staticGenerationAsyncStorage.getStore();\n    if (!store) {\n        // we assume we are in a route handler or page render. just return the searchParams\n        return searchParams;\n    } else if (store.forceStatic) {\n        // If we forced static we omit searchParams entirely. This is true both during SSR\n        // and browser render because we need there to be parity between these environments\n        return {};\n    } else if (!store.isStaticGeneration && !store.dynamicShouldError) {\n        // during dynamic renders we don't actually have to track anything so we just return\n        // the searchParams directly. However if dynamic data access should error then we\n        // still want to track access. This covers the case in Dev where all renders are dynamic\n        // but we still want to error if you use a dynamic data source because it will fail the build\n        // or revalidate if you do.\n        return searchParams;\n    } else {\n        // We need to track dynamic access with a Proxy. We implement get, has, and ownKeys because\n        // these can all be used to exfiltrate information about searchParams.\n        return new Proxy({}, {\n            get (target, prop, receiver) {\n                if (typeof prop === \"string\") {\n                    (0, _dynamicrendering.trackDynamicDataAccessed)(store, \"searchParams.\" + prop);\n                }\n                return _reflect.ReflectAdapter.get(target, prop, receiver);\n            },\n            has (target, prop) {\n                if (typeof prop === \"string\") {\n                    (0, _dynamicrendering.trackDynamicDataAccessed)(store, \"searchParams.\" + prop);\n                }\n                return Reflect.has(target, prop);\n            },\n            ownKeys (target) {\n                (0, _dynamicrendering.trackDynamicDataAccessed)(store, \"searchParams\");\n                return Reflect.ownKeys(target);\n            }\n        });\n    }\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=search-params.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/next/dist/client/components/search-params.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/next/dist/client/components/static-generation-bailout.js":
/*!********************************************************************************!*\
  !*** ../node_modules/next/dist/client/components/static-generation-bailout.js ***!
  \********************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    StaticGenBailoutError: function() {\n        return StaticGenBailoutError;\n    },\n    isStaticGenBailoutError: function() {\n        return isStaticGenBailoutError;\n    }\n});\nconst NEXT_STATIC_GEN_BAILOUT = \"NEXT_STATIC_GEN_BAILOUT\";\nclass StaticGenBailoutError extends Error {\n    constructor(...args){\n        super(...args);\n        this.code = NEXT_STATIC_GEN_BAILOUT;\n    }\n}\nfunction isStaticGenBailoutError(error) {\n    if (typeof error !== \"object\" || error === null || !(\"code\" in error)) {\n        return false;\n    }\n    return error.code === NEXT_STATIC_GEN_BAILOUT;\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=static-generation-bailout.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3N0YXRpYy1nZW5lcmF0aW9uLWJhaWxvdXQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0lBRWFBLHVCQUFxQjtlQUFyQkE7O0lBSUdDLHlCQUF1QjtlQUF2QkE7OztBQU5oQixNQUFNQywwQkFBMEI7QUFFekIsTUFBTUYsOEJBQThCRzs7O2FBQ3pCQyxJQUFBQSxHQUFPRjs7QUFDekI7QUFFTyxTQUFTRCx3QkFDZEksS0FBYztJQUVkLElBQUksT0FBT0EsVUFBVSxZQUFZQSxVQUFVLFFBQVEsQ0FBRSxXQUFVQSxLQUFBQSxHQUFRO1FBQ3JFLE9BQU87SUFDVDtJQUVBLE9BQU9BLE1BQU1ELElBQUksS0FBS0Y7QUFDeEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uLy4uL3NyYy9jbGllbnQvY29tcG9uZW50cy9zdGF0aWMtZ2VuZXJhdGlvbi1iYWlsb3V0LnRzPzRjMzEiXSwibmFtZXMiOlsiU3RhdGljR2VuQmFpbG91dEVycm9yIiwiaXNTdGF0aWNHZW5CYWlsb3V0RXJyb3IiLCJORVhUX1NUQVRJQ19HRU5fQkFJTE9VVCIsIkVycm9yIiwiY29kZSIsImVycm9yIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/next/dist/client/components/static-generation-bailout.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/next/dist/shared/lib/router/utils/handle-smooth-scroll.js":
/*!*********************************************************************************!*\
  !*** ../node_modules/next/dist/shared/lib/router/utils/handle-smooth-scroll.js ***!
  \*********************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/**\n * Run function with `scroll-behavior: auto` applied to `<html/>`.\n * This css change will be reverted after the function finishes.\n */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"handleSmoothScroll\", ({\n    enumerable: true,\n    get: function() {\n        return handleSmoothScroll;\n    }\n}));\nfunction handleSmoothScroll(fn, options) {\n    if (options === void 0) options = {};\n    // if only the hash is changed, we don't need to disable smooth scrolling\n    // we only care to prevent smooth scrolling when navigating to a new page to avoid jarring UX\n    if (options.onlyHashChange) {\n        fn();\n        return;\n    }\n    const htmlElement = document.documentElement;\n    const existing = htmlElement.style.scrollBehavior;\n    htmlElement.style.scrollBehavior = \"auto\";\n    if (!options.dontForceLayout) {\n        // In Chrome-based browsers we need to force reflow before calling `scrollTo`.\n        // Otherwise it will not pickup the change in scrollBehavior\n        // More info here: https://github.com/vercel/next.js/issues/40719#issuecomment-1336248042\n        htmlElement.getClientRects();\n    }\n    fn();\n    htmlElement.style.scrollBehavior = existing;\n} //# sourceMappingURL=handle-smooth-scroll.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NoYXJlZC9saWIvcm91dGVyL3V0aWxzL2hhbmRsZS1zbW9vdGgtc2Nyb2xsLmpzIiwibWFwcGluZ3MiOiJBQUFBOzs7Q0FHQzs7OztzREFDZUE7OztlQUFBQTs7O0FBQVQsU0FBU0EsbUJBQ2RDLEVBQWMsRUFDZEMsT0FBcUU7SUFBckVBLElBQUFBLFlBQUFBLEtBQUFBLEdBQUFBLFVBQW1FLENBQUM7SUFFcEUseUVBQXlFO0lBQ3pFLDZGQUE2RjtJQUM3RixJQUFJQSxRQUFRQyxjQUFjLEVBQUU7UUFDMUJGO1FBQ0E7SUFDRjtJQUNBLE1BQU1HLGNBQWNDLFNBQVNDLGVBQWU7SUFDNUMsTUFBTUMsV0FBV0gsWUFBWUksS0FBSyxDQUFDQyxjQUFjO0lBQ2pETCxZQUFZSSxLQUFLLENBQUNDLGNBQWMsR0FBRztJQUNuQyxJQUFJLENBQUNQLFFBQVFRLGVBQWUsRUFBRTtRQUM1Qiw4RUFBOEU7UUFDOUUsNERBQTREO1FBQzVELHlGQUF5RjtRQUN6Rk4sWUFBWU8sY0FBYztJQUM1QjtJQUNBVjtJQUNBRyxZQUFZSSxLQUFLLENBQUNDLGNBQWMsR0FBR0Y7QUFDckMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uLy4uL3NyYy9zaGFyZWQvbGliL3JvdXRlci91dGlscy9oYW5kbGUtc21vb3RoLXNjcm9sbC50cz9hYjZhIl0sIm5hbWVzIjpbImhhbmRsZVNtb290aFNjcm9sbCIsImZuIiwib3B0aW9ucyIsIm9ubHlIYXNoQ2hhbmdlIiwiaHRtbEVsZW1lbnQiLCJkb2N1bWVudCIsImRvY3VtZW50RWxlbWVudCIsImV4aXN0aW5nIiwic3R5bGUiLCJzY3JvbGxCZWhhdmlvciIsImRvbnRGb3JjZUxheW91dCIsImdldENsaWVudFJlY3RzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/next/dist/shared/lib/router/utils/handle-smooth-scroll.js\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["main-app"], function() { return __webpack_exec__("(app-pages-browser)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CHelloBeauty-New%5C%5Chello-beauty-mono%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CHelloBeauty-New%5C%5Chello-beauty-mono%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CHelloBeauty-New%5C%5Chello-beauty-mono%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CHelloBeauty-New%5C%5Chello-beauty-mono%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CHelloBeauty-New%5C%5Chello-beauty-mono%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CHelloBeauty-New%5C%5Chello-beauty-mono%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);