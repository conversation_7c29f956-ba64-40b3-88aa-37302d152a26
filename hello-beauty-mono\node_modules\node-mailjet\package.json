{"name": "node-mailjet", "version": "6.0.9", "main": "./mailjet.node.js", "browser": "./mailjet.web.js", "types": "./declarations/index.d.ts", "description": "Mailjet API client", "author": "Mailjet", "license": "MIT", "private": false, "keywords": ["email", "sms", "node", "mail", "mailjet"], "engines": {"node": ">= 12.0.0", "npm": ">= 6.9.0"}, "files": ["*"], "typescript": {"definition": "./declarations/index.d.ts"}, "dependencies": {"axios": "^1.8.1", "json-bigint": "^1.0.0", "url-join": "^4.0.0"}, "homepage": "https://github.com/mailjet/mailjet-apiv3-nodejs#readme", "repository": {"type": "git", "url": "git+https://github.com/mailjet/mailjet-apiv3-nodejs.git"}, "bugs": {"url": "https://github.com/mailjet/mailjet-apiv3-nodejs/issues"}, "contributors": ["<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/arnaud<PERSON>ton)", "<PERSON> <<EMAIL>> (https://github.com/safani)", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/p-j)"]}