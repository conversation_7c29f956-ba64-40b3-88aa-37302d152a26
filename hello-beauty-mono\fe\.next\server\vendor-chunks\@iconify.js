"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@iconify";
exports.ids = ["vendor-chunks/@iconify"];
exports.modules = {

/***/ "(ssr)/../node_modules/@iconify/react/dist/iconify.js":
/*!******************************************************!*\
  !*** ../node_modules/@iconify/react/dist/iconify.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Icon: () => (/* binding */ Icon),\n/* harmony export */   InlineIcon: () => (/* binding */ InlineIcon),\n/* harmony export */   _api: () => (/* binding */ _api),\n/* harmony export */   addAPIProvider: () => (/* binding */ addAPIProvider),\n/* harmony export */   addCollection: () => (/* binding */ addCollection),\n/* harmony export */   addIcon: () => (/* binding */ addIcon),\n/* harmony export */   buildIcon: () => (/* binding */ iconToSVG),\n/* harmony export */   calculateSize: () => (/* binding */ calculateSize),\n/* harmony export */   disableCache: () => (/* binding */ disableCache),\n/* harmony export */   enableCache: () => (/* binding */ enableCache),\n/* harmony export */   getIcon: () => (/* binding */ getIcon),\n/* harmony export */   iconExists: () => (/* binding */ iconLoaded),\n/* harmony export */   iconLoaded: () => (/* binding */ iconLoaded),\n/* harmony export */   listIcons: () => (/* binding */ listIcons),\n/* harmony export */   loadIcon: () => (/* binding */ loadIcon),\n/* harmony export */   loadIcons: () => (/* binding */ loadIcons),\n/* harmony export */   replaceIDs: () => (/* binding */ replaceIDs),\n/* harmony export */   setCustomIconLoader: () => (/* binding */ setCustomIconLoader),\n/* harmony export */   setCustomIconsLoader: () => (/* binding */ setCustomIconsLoader)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ Icon,InlineIcon,_api,addAPIProvider,addCollection,addIcon,buildIcon,calculateSize,disableCache,enableCache,getIcon,iconExists,iconLoaded,listIcons,loadIcon,loadIcons,replaceIDs,setCustomIconLoader,setCustomIconsLoader auto */ \nconst defaultIconDimensions = Object.freeze({\n    left: 0,\n    top: 0,\n    width: 16,\n    height: 16\n});\nconst defaultIconTransformations = Object.freeze({\n    rotate: 0,\n    vFlip: false,\n    hFlip: false\n});\nconst defaultIconProps = Object.freeze({\n    ...defaultIconDimensions,\n    ...defaultIconTransformations\n});\nconst defaultExtendedIconProps = Object.freeze({\n    ...defaultIconProps,\n    body: \"\",\n    hidden: false\n});\nfunction mergeIconTransformations(obj1, obj2) {\n    const result = {};\n    if (!obj1.hFlip !== !obj2.hFlip) {\n        result.hFlip = true;\n    }\n    if (!obj1.vFlip !== !obj2.vFlip) {\n        result.vFlip = true;\n    }\n    const rotate = ((obj1.rotate || 0) + (obj2.rotate || 0)) % 4;\n    if (rotate) {\n        result.rotate = rotate;\n    }\n    return result;\n}\nfunction mergeIconData(parent, child) {\n    const result = mergeIconTransformations(parent, child);\n    for(const key in defaultExtendedIconProps){\n        if (key in defaultIconTransformations) {\n            if (key in parent && !(key in result)) {\n                result[key] = defaultIconTransformations[key];\n            }\n        } else if (key in child) {\n            result[key] = child[key];\n        } else if (key in parent) {\n            result[key] = parent[key];\n        }\n    }\n    return result;\n}\nfunction getIconsTree(data, names) {\n    const icons = data.icons;\n    const aliases = data.aliases || /* @__PURE__ */ Object.create(null);\n    const resolved = /* @__PURE__ */ Object.create(null);\n    function resolve(name) {\n        if (icons[name]) {\n            return resolved[name] = [];\n        }\n        if (!(name in resolved)) {\n            resolved[name] = null;\n            const parent = aliases[name] && aliases[name].parent;\n            const value = parent && resolve(parent);\n            if (value) {\n                resolved[name] = [\n                    parent\n                ].concat(value);\n            }\n        }\n        return resolved[name];\n    }\n    Object.keys(icons).concat(Object.keys(aliases)).forEach(resolve);\n    return resolved;\n}\nfunction internalGetIconData(data, name, tree) {\n    const icons = data.icons;\n    const aliases = data.aliases || /* @__PURE__ */ Object.create(null);\n    let currentProps = {};\n    function parse(name2) {\n        currentProps = mergeIconData(icons[name2] || aliases[name2], currentProps);\n    }\n    parse(name);\n    tree.forEach(parse);\n    return mergeIconData(data, currentProps);\n}\nfunction parseIconSet(data, callback) {\n    const names = [];\n    if (typeof data !== \"object\" || typeof data.icons !== \"object\") {\n        return names;\n    }\n    if (data.not_found instanceof Array) {\n        data.not_found.forEach((name)=>{\n            callback(name, null);\n            names.push(name);\n        });\n    }\n    const tree = getIconsTree(data);\n    for(const name in tree){\n        const item = tree[name];\n        if (item) {\n            callback(name, internalGetIconData(data, name, item));\n            names.push(name);\n        }\n    }\n    return names;\n}\nconst optionalPropertyDefaults = {\n    provider: \"\",\n    aliases: {},\n    not_found: {},\n    ...defaultIconDimensions\n};\nfunction checkOptionalProps(item, defaults) {\n    for(const prop in defaults){\n        if (prop in item && typeof item[prop] !== typeof defaults[prop]) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction quicklyValidateIconSet(obj) {\n    if (typeof obj !== \"object\" || obj === null) {\n        return null;\n    }\n    const data = obj;\n    if (typeof data.prefix !== \"string\" || !obj.icons || typeof obj.icons !== \"object\") {\n        return null;\n    }\n    if (!checkOptionalProps(obj, optionalPropertyDefaults)) {\n        return null;\n    }\n    const icons = data.icons;\n    for(const name in icons){\n        const icon = icons[name];\n        if (// Name cannot be empty\n        !name || // Must have body\n        typeof icon.body !== \"string\" || // Check other props\n        !checkOptionalProps(icon, defaultExtendedIconProps)) {\n            return null;\n        }\n    }\n    const aliases = data.aliases || /* @__PURE__ */ Object.create(null);\n    for(const name in aliases){\n        const icon = aliases[name];\n        const parent = icon.parent;\n        if (// Name cannot be empty\n        !name || // Parent must be set and point to existing icon\n        typeof parent !== \"string\" || !icons[parent] && !aliases[parent] || // Check other props\n        !checkOptionalProps(icon, defaultExtendedIconProps)) {\n            return null;\n        }\n    }\n    return data;\n}\nconst matchIconName = /^[a-z0-9]+(-[a-z0-9]+)*$/;\nconst stringToIcon = (value, validate, allowSimpleName, provider = \"\")=>{\n    const colonSeparated = value.split(\":\");\n    if (value.slice(0, 1) === \"@\") {\n        if (colonSeparated.length < 2 || colonSeparated.length > 3) {\n            return null;\n        }\n        provider = colonSeparated.shift().slice(1);\n    }\n    if (colonSeparated.length > 3 || !colonSeparated.length) {\n        return null;\n    }\n    if (colonSeparated.length > 1) {\n        const name2 = colonSeparated.pop();\n        const prefix = colonSeparated.pop();\n        const result = {\n            // Allow provider without '@': \"provider:prefix:name\"\n            provider: colonSeparated.length > 0 ? colonSeparated[0] : provider,\n            prefix,\n            name: name2\n        };\n        return validate && !validateIconName(result) ? null : result;\n    }\n    const name = colonSeparated[0];\n    const dashSeparated = name.split(\"-\");\n    if (dashSeparated.length > 1) {\n        const result = {\n            provider,\n            prefix: dashSeparated.shift(),\n            name: dashSeparated.join(\"-\")\n        };\n        return validate && !validateIconName(result) ? null : result;\n    }\n    if (allowSimpleName && provider === \"\") {\n        const result = {\n            provider,\n            prefix: \"\",\n            name\n        };\n        return validate && !validateIconName(result, allowSimpleName) ? null : result;\n    }\n    return null;\n};\nconst validateIconName = (icon, allowSimpleName)=>{\n    if (!icon) {\n        return false;\n    }\n    return !!// Check name: cannot be empty\n    ((allowSimpleName && icon.prefix === \"\" || !!icon.prefix) && !!icon.name);\n};\nconst dataStorage = /* @__PURE__ */ Object.create(null);\nfunction newStorage(provider, prefix) {\n    return {\n        provider,\n        prefix,\n        icons: /* @__PURE__ */ Object.create(null),\n        missing: /* @__PURE__ */ new Set()\n    };\n}\nfunction getStorage(provider, prefix) {\n    const providerStorage = dataStorage[provider] || (dataStorage[provider] = /* @__PURE__ */ Object.create(null));\n    return providerStorage[prefix] || (providerStorage[prefix] = newStorage(provider, prefix));\n}\nfunction addIconSet(storage, data) {\n    if (!quicklyValidateIconSet(data)) {\n        return [];\n    }\n    return parseIconSet(data, (name, icon)=>{\n        if (icon) {\n            storage.icons[name] = icon;\n        } else {\n            storage.missing.add(name);\n        }\n    });\n}\nfunction addIconToStorage(storage, name, icon) {\n    try {\n        if (typeof icon.body === \"string\") {\n            storage.icons[name] = {\n                ...icon\n            };\n            return true;\n        }\n    } catch (err) {}\n    return false;\n}\nfunction listIcons(provider, prefix) {\n    let allIcons = [];\n    const providers = typeof provider === \"string\" ? [\n        provider\n    ] : Object.keys(dataStorage);\n    providers.forEach((provider2)=>{\n        const prefixes = typeof provider2 === \"string\" && typeof prefix === \"string\" ? [\n            prefix\n        ] : Object.keys(dataStorage[provider2] || {});\n        prefixes.forEach((prefix2)=>{\n            const storage = getStorage(provider2, prefix2);\n            allIcons = allIcons.concat(Object.keys(storage.icons).map((name)=>(provider2 !== \"\" ? \"@\" + provider2 + \":\" : \"\") + prefix2 + \":\" + name));\n        });\n    });\n    return allIcons;\n}\nlet simpleNames = false;\nfunction allowSimpleNames(allow) {\n    if (typeof allow === \"boolean\") {\n        simpleNames = allow;\n    }\n    return simpleNames;\n}\nfunction getIconData(name) {\n    const icon = typeof name === \"string\" ? stringToIcon(name, true, simpleNames) : name;\n    if (icon) {\n        const storage = getStorage(icon.provider, icon.prefix);\n        const iconName = icon.name;\n        return storage.icons[iconName] || (storage.missing.has(iconName) ? null : void 0);\n    }\n}\nfunction addIcon(name, data) {\n    const icon = stringToIcon(name, true, simpleNames);\n    if (!icon) {\n        return false;\n    }\n    const storage = getStorage(icon.provider, icon.prefix);\n    if (data) {\n        return addIconToStorage(storage, icon.name, data);\n    } else {\n        storage.missing.add(icon.name);\n        return true;\n    }\n}\nfunction addCollection(data, provider) {\n    if (typeof data !== \"object\") {\n        return false;\n    }\n    if (typeof provider !== \"string\") {\n        provider = data.provider || \"\";\n    }\n    if (simpleNames && !provider && !data.prefix) {\n        let added = false;\n        if (quicklyValidateIconSet(data)) {\n            data.prefix = \"\";\n            parseIconSet(data, (name, icon)=>{\n                if (addIcon(name, icon)) {\n                    added = true;\n                }\n            });\n        }\n        return added;\n    }\n    const prefix = data.prefix;\n    if (!validateIconName({\n        prefix,\n        name: \"a\"\n    })) {\n        return false;\n    }\n    const storage = getStorage(provider, prefix);\n    return !!addIconSet(storage, data);\n}\nfunction iconLoaded(name) {\n    return !!getIconData(name);\n}\nfunction getIcon(name) {\n    const result = getIconData(name);\n    return result ? {\n        ...defaultIconProps,\n        ...result\n    } : result;\n}\nconst defaultIconSizeCustomisations = Object.freeze({\n    width: null,\n    height: null\n});\nconst defaultIconCustomisations = Object.freeze({\n    // Dimensions\n    ...defaultIconSizeCustomisations,\n    // Transformations\n    ...defaultIconTransformations\n});\nconst unitsSplit = /(-?[0-9.]*[0-9]+[0-9.]*)/g;\nconst unitsTest = /^-?[0-9.]*[0-9]+[0-9.]*$/g;\nfunction calculateSize(size, ratio, precision) {\n    if (ratio === 1) {\n        return size;\n    }\n    precision = precision || 100;\n    if (typeof size === \"number\") {\n        return Math.ceil(size * ratio * precision) / precision;\n    }\n    if (typeof size !== \"string\") {\n        return size;\n    }\n    const oldParts = size.split(unitsSplit);\n    if (oldParts === null || !oldParts.length) {\n        return size;\n    }\n    const newParts = [];\n    let code = oldParts.shift();\n    let isNumber = unitsTest.test(code);\n    while(true){\n        if (isNumber) {\n            const num = parseFloat(code);\n            if (isNaN(num)) {\n                newParts.push(code);\n            } else {\n                newParts.push(Math.ceil(num * ratio * precision) / precision);\n            }\n        } else {\n            newParts.push(code);\n        }\n        code = oldParts.shift();\n        if (code === void 0) {\n            return newParts.join(\"\");\n        }\n        isNumber = !isNumber;\n    }\n}\nfunction splitSVGDefs(content, tag = \"defs\") {\n    let defs = \"\";\n    const index = content.indexOf(\"<\" + tag);\n    while(index >= 0){\n        const start = content.indexOf(\">\", index);\n        const end = content.indexOf(\"</\" + tag);\n        if (start === -1 || end === -1) {\n            break;\n        }\n        const endEnd = content.indexOf(\">\", end);\n        if (endEnd === -1) {\n            break;\n        }\n        defs += content.slice(start + 1, end).trim();\n        content = content.slice(0, index).trim() + content.slice(endEnd + 1);\n    }\n    return {\n        defs,\n        content\n    };\n}\nfunction mergeDefsAndContent(defs, content) {\n    return defs ? \"<defs>\" + defs + \"</defs>\" + content : content;\n}\nfunction wrapSVGContent(body, start, end) {\n    const split = splitSVGDefs(body);\n    return mergeDefsAndContent(split.defs, start + split.content + end);\n}\nconst isUnsetKeyword = (value)=>value === \"unset\" || value === \"undefined\" || value === \"none\";\nfunction iconToSVG(icon, customisations) {\n    const fullIcon = {\n        ...defaultIconProps,\n        ...icon\n    };\n    const fullCustomisations = {\n        ...defaultIconCustomisations,\n        ...customisations\n    };\n    const box = {\n        left: fullIcon.left,\n        top: fullIcon.top,\n        width: fullIcon.width,\n        height: fullIcon.height\n    };\n    let body = fullIcon.body;\n    [\n        fullIcon,\n        fullCustomisations\n    ].forEach((props)=>{\n        const transformations = [];\n        const hFlip = props.hFlip;\n        const vFlip = props.vFlip;\n        let rotation = props.rotate;\n        if (hFlip) {\n            if (vFlip) {\n                rotation += 2;\n            } else {\n                transformations.push(\"translate(\" + (box.width + box.left).toString() + \" \" + (0 - box.top).toString() + \")\");\n                transformations.push(\"scale(-1 1)\");\n                box.top = box.left = 0;\n            }\n        } else if (vFlip) {\n            transformations.push(\"translate(\" + (0 - box.left).toString() + \" \" + (box.height + box.top).toString() + \")\");\n            transformations.push(\"scale(1 -1)\");\n            box.top = box.left = 0;\n        }\n        let tempValue;\n        if (rotation < 0) {\n            rotation -= Math.floor(rotation / 4) * 4;\n        }\n        rotation = rotation % 4;\n        switch(rotation){\n            case 1:\n                tempValue = box.height / 2 + box.top;\n                transformations.unshift(\"rotate(90 \" + tempValue.toString() + \" \" + tempValue.toString() + \")\");\n                break;\n            case 2:\n                transformations.unshift(\"rotate(180 \" + (box.width / 2 + box.left).toString() + \" \" + (box.height / 2 + box.top).toString() + \")\");\n                break;\n            case 3:\n                tempValue = box.width / 2 + box.left;\n                transformations.unshift(\"rotate(-90 \" + tempValue.toString() + \" \" + tempValue.toString() + \")\");\n                break;\n        }\n        if (rotation % 2 === 1) {\n            if (box.left !== box.top) {\n                tempValue = box.left;\n                box.left = box.top;\n                box.top = tempValue;\n            }\n            if (box.width !== box.height) {\n                tempValue = box.width;\n                box.width = box.height;\n                box.height = tempValue;\n            }\n        }\n        if (transformations.length) {\n            body = wrapSVGContent(body, '<g transform=\"' + transformations.join(\" \") + '\">', \"</g>\");\n        }\n    });\n    const customisationsWidth = fullCustomisations.width;\n    const customisationsHeight = fullCustomisations.height;\n    const boxWidth = box.width;\n    const boxHeight = box.height;\n    let width;\n    let height;\n    if (customisationsWidth === null) {\n        height = customisationsHeight === null ? \"1em\" : customisationsHeight === \"auto\" ? boxHeight : customisationsHeight;\n        width = calculateSize(height, boxWidth / boxHeight);\n    } else {\n        width = customisationsWidth === \"auto\" ? boxWidth : customisationsWidth;\n        height = customisationsHeight === null ? calculateSize(width, boxHeight / boxWidth) : customisationsHeight === \"auto\" ? boxHeight : customisationsHeight;\n    }\n    const attributes = {};\n    const setAttr = (prop, value)=>{\n        if (!isUnsetKeyword(value)) {\n            attributes[prop] = value.toString();\n        }\n    };\n    setAttr(\"width\", width);\n    setAttr(\"height\", height);\n    const viewBox = [\n        box.left,\n        box.top,\n        boxWidth,\n        boxHeight\n    ];\n    attributes.viewBox = viewBox.join(\" \");\n    return {\n        attributes,\n        viewBox,\n        body\n    };\n}\nconst regex = /\\sid=\"(\\S+)\"/g;\nconst randomPrefix = \"IconifyId\" + Date.now().toString(16) + (Math.random() * 16777216 | 0).toString(16);\nlet counter = 0;\nfunction replaceIDs(body, prefix = randomPrefix) {\n    const ids = [];\n    let match;\n    while(match = regex.exec(body)){\n        ids.push(match[1]);\n    }\n    if (!ids.length) {\n        return body;\n    }\n    const suffix = \"suffix\" + (Math.random() * 16777216 | Date.now()).toString(16);\n    ids.forEach((id)=>{\n        const newID = typeof prefix === \"function\" ? prefix(id) : prefix + (counter++).toString();\n        const escapedID = id.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n        body = body.replace(// Allowed characters before id: [#;\"]\n        // Allowed characters after id: [)\"], .[a-z]\n        new RegExp('([#;\"])(' + escapedID + ')([\")]|\\\\.[a-z])', \"g\"), \"$1\" + newID + suffix + \"$3\");\n    });\n    body = body.replace(new RegExp(suffix, \"g\"), \"\");\n    return body;\n}\nconst storage = /* @__PURE__ */ Object.create(null);\nfunction setAPIModule(provider, item) {\n    storage[provider] = item;\n}\nfunction getAPIModule(provider) {\n    return storage[provider] || storage[\"\"];\n}\nfunction createAPIConfig(source) {\n    let resources;\n    if (typeof source.resources === \"string\") {\n        resources = [\n            source.resources\n        ];\n    } else {\n        resources = source.resources;\n        if (!(resources instanceof Array) || !resources.length) {\n            return null;\n        }\n    }\n    const result = {\n        // API hosts\n        resources,\n        // Root path\n        path: source.path || \"/\",\n        // URL length limit\n        maxURL: source.maxURL || 500,\n        // Timeout before next host is used.\n        rotate: source.rotate || 750,\n        // Timeout before failing query.\n        timeout: source.timeout || 5e3,\n        // Randomise default API end point.\n        random: source.random === true,\n        // Start index\n        index: source.index || 0,\n        // Receive data after time out (used if time out kicks in first, then API module sends data anyway).\n        dataAfterTimeout: source.dataAfterTimeout !== false\n    };\n    return result;\n}\nconst configStorage = /* @__PURE__ */ Object.create(null);\nconst fallBackAPISources = [\n    \"https://api.simplesvg.com\",\n    \"https://api.unisvg.com\"\n];\nconst fallBackAPI = [];\nwhile(fallBackAPISources.length > 0){\n    if (fallBackAPISources.length === 1) {\n        fallBackAPI.push(fallBackAPISources.shift());\n    } else {\n        if (Math.random() > 0.5) {\n            fallBackAPI.push(fallBackAPISources.shift());\n        } else {\n            fallBackAPI.push(fallBackAPISources.pop());\n        }\n    }\n}\nconfigStorage[\"\"] = createAPIConfig({\n    resources: [\n        \"https://api.iconify.design\"\n    ].concat(fallBackAPI)\n});\nfunction addAPIProvider(provider, customConfig) {\n    const config = createAPIConfig(customConfig);\n    if (config === null) {\n        return false;\n    }\n    configStorage[provider] = config;\n    return true;\n}\nfunction getAPIConfig(provider) {\n    return configStorage[provider];\n}\nfunction listAPIProviders() {\n    return Object.keys(configStorage);\n}\nconst detectFetch = ()=>{\n    let callback;\n    try {\n        callback = fetch;\n        if (typeof callback === \"function\") {\n            return callback;\n        }\n    } catch (err) {}\n};\nlet fetchModule = detectFetch();\nfunction setFetch(fetch2) {\n    fetchModule = fetch2;\n}\nfunction getFetch() {\n    return fetchModule;\n}\nfunction calculateMaxLength(provider, prefix) {\n    const config = getAPIConfig(provider);\n    if (!config) {\n        return 0;\n    }\n    let result;\n    if (!config.maxURL) {\n        result = 0;\n    } else {\n        let maxHostLength = 0;\n        config.resources.forEach((item)=>{\n            const host = item;\n            maxHostLength = Math.max(maxHostLength, host.length);\n        });\n        const url = prefix + \".json?icons=\";\n        result = config.maxURL - maxHostLength - config.path.length - url.length;\n    }\n    return result;\n}\nfunction shouldAbort(status) {\n    return status === 404;\n}\nconst prepare = (provider, prefix, icons)=>{\n    const results = [];\n    const maxLength = calculateMaxLength(provider, prefix);\n    const type = \"icons\";\n    let item = {\n        type,\n        provider,\n        prefix,\n        icons: []\n    };\n    let length = 0;\n    icons.forEach((name, index)=>{\n        length += name.length + 1;\n        if (length >= maxLength && index > 0) {\n            results.push(item);\n            item = {\n                type,\n                provider,\n                prefix,\n                icons: []\n            };\n            length = name.length;\n        }\n        item.icons.push(name);\n    });\n    results.push(item);\n    return results;\n};\nfunction getPath(provider) {\n    if (typeof provider === \"string\") {\n        const config = getAPIConfig(provider);\n        if (config) {\n            return config.path;\n        }\n    }\n    return \"/\";\n}\nconst send = (host, params, callback)=>{\n    if (!fetchModule) {\n        callback(\"abort\", 424);\n        return;\n    }\n    let path = getPath(params.provider);\n    switch(params.type){\n        case \"icons\":\n            {\n                const prefix = params.prefix;\n                const icons = params.icons;\n                const iconsList = icons.join(\",\");\n                const urlParams = new URLSearchParams({\n                    icons: iconsList\n                });\n                path += prefix + \".json?\" + urlParams.toString();\n                break;\n            }\n        case \"custom\":\n            {\n                const uri = params.uri;\n                path += uri.slice(0, 1) === \"/\" ? uri.slice(1) : uri;\n                break;\n            }\n        default:\n            callback(\"abort\", 400);\n            return;\n    }\n    let defaultError = 503;\n    fetchModule(host + path).then((response)=>{\n        const status = response.status;\n        if (status !== 200) {\n            setTimeout(()=>{\n                callback(shouldAbort(status) ? \"abort\" : \"next\", status);\n            });\n            return;\n        }\n        defaultError = 501;\n        return response.json();\n    }).then((data)=>{\n        if (typeof data !== \"object\" || data === null) {\n            setTimeout(()=>{\n                if (data === 404) {\n                    callback(\"abort\", data);\n                } else {\n                    callback(\"next\", defaultError);\n                }\n            });\n            return;\n        }\n        setTimeout(()=>{\n            callback(\"success\", data);\n        });\n    }).catch(()=>{\n        callback(\"next\", defaultError);\n    });\n};\nconst fetchAPIModule = {\n    prepare,\n    send\n};\nfunction sortIcons(icons) {\n    const result = {\n        loaded: [],\n        missing: [],\n        pending: []\n    };\n    const storage = /* @__PURE__ */ Object.create(null);\n    icons.sort((a, b)=>{\n        if (a.provider !== b.provider) {\n            return a.provider.localeCompare(b.provider);\n        }\n        if (a.prefix !== b.prefix) {\n            return a.prefix.localeCompare(b.prefix);\n        }\n        return a.name.localeCompare(b.name);\n    });\n    let lastIcon = {\n        provider: \"\",\n        prefix: \"\",\n        name: \"\"\n    };\n    icons.forEach((icon)=>{\n        if (lastIcon.name === icon.name && lastIcon.prefix === icon.prefix && lastIcon.provider === icon.provider) {\n            return;\n        }\n        lastIcon = icon;\n        const provider = icon.provider;\n        const prefix = icon.prefix;\n        const name = icon.name;\n        const providerStorage = storage[provider] || (storage[provider] = /* @__PURE__ */ Object.create(null));\n        const localStorage = providerStorage[prefix] || (providerStorage[prefix] = getStorage(provider, prefix));\n        let list;\n        if (name in localStorage.icons) {\n            list = result.loaded;\n        } else if (prefix === \"\" || localStorage.missing.has(name)) {\n            list = result.missing;\n        } else {\n            list = result.pending;\n        }\n        const item = {\n            provider,\n            prefix,\n            name\n        };\n        list.push(item);\n    });\n    return result;\n}\nfunction removeCallback(storages, id) {\n    storages.forEach((storage)=>{\n        const items = storage.loaderCallbacks;\n        if (items) {\n            storage.loaderCallbacks = items.filter((row)=>row.id !== id);\n        }\n    });\n}\nfunction updateCallbacks(storage) {\n    if (!storage.pendingCallbacksFlag) {\n        storage.pendingCallbacksFlag = true;\n        setTimeout(()=>{\n            storage.pendingCallbacksFlag = false;\n            const items = storage.loaderCallbacks ? storage.loaderCallbacks.slice(0) : [];\n            if (!items.length) {\n                return;\n            }\n            let hasPending = false;\n            const provider = storage.provider;\n            const prefix = storage.prefix;\n            items.forEach((item)=>{\n                const icons = item.icons;\n                const oldLength = icons.pending.length;\n                icons.pending = icons.pending.filter((icon)=>{\n                    if (icon.prefix !== prefix) {\n                        return true;\n                    }\n                    const name = icon.name;\n                    if (storage.icons[name]) {\n                        icons.loaded.push({\n                            provider,\n                            prefix,\n                            name\n                        });\n                    } else if (storage.missing.has(name)) {\n                        icons.missing.push({\n                            provider,\n                            prefix,\n                            name\n                        });\n                    } else {\n                        hasPending = true;\n                        return true;\n                    }\n                    return false;\n                });\n                if (icons.pending.length !== oldLength) {\n                    if (!hasPending) {\n                        removeCallback([\n                            storage\n                        ], item.id);\n                    }\n                    item.callback(icons.loaded.slice(0), icons.missing.slice(0), icons.pending.slice(0), item.abort);\n                }\n            });\n        });\n    }\n}\nlet idCounter = 0;\nfunction storeCallback(callback, icons, pendingSources) {\n    const id = idCounter++;\n    const abort = removeCallback.bind(null, pendingSources, id);\n    if (!icons.pending.length) {\n        return abort;\n    }\n    const item = {\n        id,\n        icons,\n        callback,\n        abort\n    };\n    pendingSources.forEach((storage)=>{\n        (storage.loaderCallbacks || (storage.loaderCallbacks = [])).push(item);\n    });\n    return abort;\n}\nfunction listToIcons(list, validate = true, simpleNames = false) {\n    const result = [];\n    list.forEach((item)=>{\n        const icon = typeof item === \"string\" ? stringToIcon(item, validate, simpleNames) : item;\n        if (icon) {\n            result.push(icon);\n        }\n    });\n    return result;\n}\n// src/config.ts\nvar defaultConfig = {\n    resources: [],\n    index: 0,\n    timeout: 2e3,\n    rotate: 750,\n    random: false,\n    dataAfterTimeout: false\n};\n// src/query.ts\nfunction sendQuery(config, payload, query, done) {\n    const resourcesCount = config.resources.length;\n    const startIndex = config.random ? Math.floor(Math.random() * resourcesCount) : config.index;\n    let resources;\n    if (config.random) {\n        let list = config.resources.slice(0);\n        resources = [];\n        while(list.length > 1){\n            const nextIndex = Math.floor(Math.random() * list.length);\n            resources.push(list[nextIndex]);\n            list = list.slice(0, nextIndex).concat(list.slice(nextIndex + 1));\n        }\n        resources = resources.concat(list);\n    } else {\n        resources = config.resources.slice(startIndex).concat(config.resources.slice(0, startIndex));\n    }\n    const startTime = Date.now();\n    let status = \"pending\";\n    let queriesSent = 0;\n    let lastError;\n    let timer = null;\n    let queue = [];\n    let doneCallbacks = [];\n    if (typeof done === \"function\") {\n        doneCallbacks.push(done);\n    }\n    function resetTimer() {\n        if (timer) {\n            clearTimeout(timer);\n            timer = null;\n        }\n    }\n    function abort() {\n        if (status === \"pending\") {\n            status = \"aborted\";\n        }\n        resetTimer();\n        queue.forEach((item)=>{\n            if (item.status === \"pending\") {\n                item.status = \"aborted\";\n            }\n        });\n        queue = [];\n    }\n    function subscribe(callback, overwrite) {\n        if (overwrite) {\n            doneCallbacks = [];\n        }\n        if (typeof callback === \"function\") {\n            doneCallbacks.push(callback);\n        }\n    }\n    function getQueryStatus() {\n        return {\n            startTime,\n            payload,\n            status,\n            queriesSent,\n            queriesPending: queue.length,\n            subscribe,\n            abort\n        };\n    }\n    function failQuery() {\n        status = \"failed\";\n        doneCallbacks.forEach((callback)=>{\n            callback(void 0, lastError);\n        });\n    }\n    function clearQueue() {\n        queue.forEach((item)=>{\n            if (item.status === \"pending\") {\n                item.status = \"aborted\";\n            }\n        });\n        queue = [];\n    }\n    function moduleResponse(item, response, data) {\n        const isError = response !== \"success\";\n        queue = queue.filter((queued)=>queued !== item);\n        switch(status){\n            case \"pending\":\n                break;\n            case \"failed\":\n                if (isError || !config.dataAfterTimeout) {\n                    return;\n                }\n                break;\n            default:\n                return;\n        }\n        if (response === \"abort\") {\n            lastError = data;\n            failQuery();\n            return;\n        }\n        if (isError) {\n            lastError = data;\n            if (!queue.length) {\n                if (!resources.length) {\n                    failQuery();\n                } else {\n                    execNext();\n                }\n            }\n            return;\n        }\n        resetTimer();\n        clearQueue();\n        if (!config.random) {\n            const index = config.resources.indexOf(item.resource);\n            if (index !== -1 && index !== config.index) {\n                config.index = index;\n            }\n        }\n        status = \"completed\";\n        doneCallbacks.forEach((callback)=>{\n            callback(data);\n        });\n    }\n    function execNext() {\n        if (status !== \"pending\") {\n            return;\n        }\n        resetTimer();\n        const resource = resources.shift();\n        if (resource === void 0) {\n            if (queue.length) {\n                timer = setTimeout(()=>{\n                    resetTimer();\n                    if (status === \"pending\") {\n                        clearQueue();\n                        failQuery();\n                    }\n                }, config.timeout);\n                return;\n            }\n            failQuery();\n            return;\n        }\n        const item = {\n            status: \"pending\",\n            resource,\n            callback: (status2, data)=>{\n                moduleResponse(item, status2, data);\n            }\n        };\n        queue.push(item);\n        queriesSent++;\n        timer = setTimeout(execNext, config.rotate);\n        query(resource, payload, item.callback);\n    }\n    setTimeout(execNext);\n    return getQueryStatus;\n}\n// src/index.ts\nfunction initRedundancy(cfg) {\n    const config = {\n        ...defaultConfig,\n        ...cfg\n    };\n    let queries = [];\n    function cleanup() {\n        queries = queries.filter((item)=>item().status === \"pending\");\n    }\n    function query(payload, queryCallback, doneCallback) {\n        const query2 = sendQuery(config, payload, queryCallback, (data, error)=>{\n            cleanup();\n            if (doneCallback) {\n                doneCallback(data, error);\n            }\n        });\n        queries.push(query2);\n        return query2;\n    }\n    function find(callback) {\n        return queries.find((value)=>{\n            return callback(value);\n        }) || null;\n    }\n    const instance = {\n        query,\n        find,\n        setIndex: (index)=>{\n            config.index = index;\n        },\n        getIndex: ()=>config.index,\n        cleanup\n    };\n    return instance;\n}\nfunction emptyCallback$1() {}\nconst redundancyCache = /* @__PURE__ */ Object.create(null);\nfunction getRedundancyCache(provider) {\n    if (!redundancyCache[provider]) {\n        const config = getAPIConfig(provider);\n        if (!config) {\n            return;\n        }\n        const redundancy = initRedundancy(config);\n        const cachedReundancy = {\n            config,\n            redundancy\n        };\n        redundancyCache[provider] = cachedReundancy;\n    }\n    return redundancyCache[provider];\n}\nfunction sendAPIQuery(target, query, callback) {\n    let redundancy;\n    let send;\n    if (typeof target === \"string\") {\n        const api = getAPIModule(target);\n        if (!api) {\n            callback(void 0, 424);\n            return emptyCallback$1;\n        }\n        send = api.send;\n        const cached = getRedundancyCache(target);\n        if (cached) {\n            redundancy = cached.redundancy;\n        }\n    } else {\n        const config = createAPIConfig(target);\n        if (config) {\n            redundancy = initRedundancy(config);\n            const moduleKey = target.resources ? target.resources[0] : \"\";\n            const api = getAPIModule(moduleKey);\n            if (api) {\n                send = api.send;\n            }\n        }\n    }\n    if (!redundancy || !send) {\n        callback(void 0, 424);\n        return emptyCallback$1;\n    }\n    return redundancy.query(query, send, callback)().abort;\n}\nfunction emptyCallback() {}\nfunction loadedNewIcons(storage) {\n    if (!storage.iconsLoaderFlag) {\n        storage.iconsLoaderFlag = true;\n        setTimeout(()=>{\n            storage.iconsLoaderFlag = false;\n            updateCallbacks(storage);\n        });\n    }\n}\nfunction checkIconNamesForAPI(icons) {\n    const valid = [];\n    const invalid = [];\n    icons.forEach((name)=>{\n        (name.match(matchIconName) ? valid : invalid).push(name);\n    });\n    return {\n        valid,\n        invalid\n    };\n}\nfunction parseLoaderResponse(storage, icons, data) {\n    function checkMissing() {\n        const pending = storage.pendingIcons;\n        icons.forEach((name)=>{\n            if (pending) {\n                pending.delete(name);\n            }\n            if (!storage.icons[name]) {\n                storage.missing.add(name);\n            }\n        });\n    }\n    if (data && typeof data === \"object\") {\n        try {\n            const parsed = addIconSet(storage, data);\n            if (!parsed.length) {\n                checkMissing();\n                return;\n            }\n        } catch (err) {\n            console.error(err);\n        }\n    }\n    checkMissing();\n    loadedNewIcons(storage);\n}\nfunction parsePossiblyAsyncResponse(response, callback) {\n    if (response instanceof Promise) {\n        response.then((data)=>{\n            callback(data);\n        }).catch(()=>{\n            callback(null);\n        });\n    } else {\n        callback(response);\n    }\n}\nfunction loadNewIcons(storage, icons) {\n    if (!storage.iconsToLoad) {\n        storage.iconsToLoad = icons;\n    } else {\n        storage.iconsToLoad = storage.iconsToLoad.concat(icons).sort();\n    }\n    if (!storage.iconsQueueFlag) {\n        storage.iconsQueueFlag = true;\n        setTimeout(()=>{\n            storage.iconsQueueFlag = false;\n            const { provider, prefix } = storage;\n            const icons2 = storage.iconsToLoad;\n            delete storage.iconsToLoad;\n            if (!icons2 || !icons2.length) {\n                return;\n            }\n            const customIconLoader = storage.loadIcon;\n            if (storage.loadIcons && (icons2.length > 1 || !customIconLoader)) {\n                parsePossiblyAsyncResponse(storage.loadIcons(icons2, prefix, provider), (data)=>{\n                    parseLoaderResponse(storage, icons2, data);\n                });\n                return;\n            }\n            if (customIconLoader) {\n                icons2.forEach((name)=>{\n                    const response = customIconLoader(name, prefix, provider);\n                    parsePossiblyAsyncResponse(response, (data)=>{\n                        const iconSet = data ? {\n                            prefix,\n                            icons: {\n                                [name]: data\n                            }\n                        } : null;\n                        parseLoaderResponse(storage, [\n                            name\n                        ], iconSet);\n                    });\n                });\n                return;\n            }\n            const { valid, invalid } = checkIconNamesForAPI(icons2);\n            if (invalid.length) {\n                parseLoaderResponse(storage, invalid, null);\n            }\n            if (!valid.length) {\n                return;\n            }\n            const api = prefix.match(matchIconName) ? getAPIModule(provider) : null;\n            if (!api) {\n                parseLoaderResponse(storage, valid, null);\n                return;\n            }\n            const params = api.prepare(provider, prefix, valid);\n            params.forEach((item)=>{\n                sendAPIQuery(provider, item, (data)=>{\n                    parseLoaderResponse(storage, item.icons, data);\n                });\n            });\n        });\n    }\n}\nconst loadIcons = (icons, callback)=>{\n    const cleanedIcons = listToIcons(icons, true, allowSimpleNames());\n    const sortedIcons = sortIcons(cleanedIcons);\n    if (!sortedIcons.pending.length) {\n        let callCallback = true;\n        if (callback) {\n            setTimeout(()=>{\n                if (callCallback) {\n                    callback(sortedIcons.loaded, sortedIcons.missing, sortedIcons.pending, emptyCallback);\n                }\n            });\n        }\n        return ()=>{\n            callCallback = false;\n        };\n    }\n    const newIcons = /* @__PURE__ */ Object.create(null);\n    const sources = [];\n    let lastProvider, lastPrefix;\n    sortedIcons.pending.forEach((icon)=>{\n        const { provider, prefix } = icon;\n        if (prefix === lastPrefix && provider === lastProvider) {\n            return;\n        }\n        lastProvider = provider;\n        lastPrefix = prefix;\n        sources.push(getStorage(provider, prefix));\n        const providerNewIcons = newIcons[provider] || (newIcons[provider] = /* @__PURE__ */ Object.create(null));\n        if (!providerNewIcons[prefix]) {\n            providerNewIcons[prefix] = [];\n        }\n    });\n    sortedIcons.pending.forEach((icon)=>{\n        const { provider, prefix, name } = icon;\n        const storage = getStorage(provider, prefix);\n        const pendingQueue = storage.pendingIcons || (storage.pendingIcons = /* @__PURE__ */ new Set());\n        if (!pendingQueue.has(name)) {\n            pendingQueue.add(name);\n            newIcons[provider][prefix].push(name);\n        }\n    });\n    sources.forEach((storage)=>{\n        const list = newIcons[storage.provider][storage.prefix];\n        if (list.length) {\n            loadNewIcons(storage, list);\n        }\n    });\n    return callback ? storeCallback(callback, sortedIcons, sources) : emptyCallback;\n};\nconst loadIcon = (icon)=>{\n    return new Promise((fulfill, reject)=>{\n        const iconObj = typeof icon === \"string\" ? stringToIcon(icon, true) : icon;\n        if (!iconObj) {\n            reject(icon);\n            return;\n        }\n        loadIcons([\n            iconObj || icon\n        ], (loaded)=>{\n            if (loaded.length && iconObj) {\n                const data = getIconData(iconObj);\n                if (data) {\n                    fulfill({\n                        ...defaultIconProps,\n                        ...data\n                    });\n                    return;\n                }\n            }\n            reject(icon);\n        });\n    });\n};\nfunction setCustomIconsLoader(loader, prefix, provider) {\n    getStorage(provider || \"\", prefix).loadIcons = loader;\n}\nfunction setCustomIconLoader(loader, prefix, provider) {\n    getStorage(provider || \"\", prefix).loadIcon = loader;\n}\nfunction mergeCustomisations(defaults, item) {\n    const result = {\n        ...defaults\n    };\n    for(const key in item){\n        const value = item[key];\n        const valueType = typeof value;\n        if (key in defaultIconSizeCustomisations) {\n            if (value === null || value && (valueType === \"string\" || valueType === \"number\")) {\n                result[key] = value;\n            }\n        } else if (valueType === typeof result[key]) {\n            result[key] = key === \"rotate\" ? value % 4 : value;\n        }\n    }\n    return result;\n}\nconst separator = /[\\s,]+/;\nfunction flipFromString(custom, flip) {\n    flip.split(separator).forEach((str)=>{\n        const value = str.trim();\n        switch(value){\n            case \"horizontal\":\n                custom.hFlip = true;\n                break;\n            case \"vertical\":\n                custom.vFlip = true;\n                break;\n        }\n    });\n}\nfunction rotateFromString(value, defaultValue = 0) {\n    const units = value.replace(/^-?[0-9.]*/, \"\");\n    function cleanup(value2) {\n        while(value2 < 0){\n            value2 += 4;\n        }\n        return value2 % 4;\n    }\n    if (units === \"\") {\n        const num = parseInt(value);\n        return isNaN(num) ? 0 : cleanup(num);\n    } else if (units !== value) {\n        let split = 0;\n        switch(units){\n            case \"%\":\n                split = 25;\n                break;\n            case \"deg\":\n                split = 90;\n        }\n        if (split) {\n            let num = parseFloat(value.slice(0, value.length - units.length));\n            if (isNaN(num)) {\n                return 0;\n            }\n            num = num / split;\n            return num % 1 === 0 ? cleanup(num) : 0;\n        }\n    }\n    return defaultValue;\n}\nfunction iconToHTML(body, attributes) {\n    let renderAttribsHTML = body.indexOf(\"xlink:\") === -1 ? \"\" : ' xmlns:xlink=\"http://www.w3.org/1999/xlink\"';\n    for(const attr in attributes){\n        renderAttribsHTML += \" \" + attr + '=\"' + attributes[attr] + '\"';\n    }\n    return '<svg xmlns=\"http://www.w3.org/2000/svg\"' + renderAttribsHTML + \">\" + body + \"</svg>\";\n}\nfunction encodeSVGforURL(svg) {\n    return svg.replace(/\"/g, \"'\").replace(/%/g, \"%25\").replace(/#/g, \"%23\").replace(/</g, \"%3C\").replace(/>/g, \"%3E\").replace(/\\s+/g, \" \");\n}\nfunction svgToData(svg) {\n    return \"data:image/svg+xml,\" + encodeSVGforURL(svg);\n}\nfunction svgToURL(svg) {\n    return 'url(\"' + svgToData(svg) + '\")';\n}\nlet policy;\nfunction createPolicy() {\n    try {\n        policy = window.trustedTypes.createPolicy(\"iconify\", {\n            // eslint-disable-next-line @typescript-eslint/no-unsafe-return\n            createHTML: (s)=>s\n        });\n    } catch (err) {\n        policy = null;\n    }\n}\nfunction cleanUpInnerHTML(html) {\n    if (policy === void 0) {\n        createPolicy();\n    }\n    return policy ? policy.createHTML(html) : html;\n}\nconst defaultExtendedIconCustomisations = {\n    ...defaultIconCustomisations,\n    inline: false\n};\n/**\n * Default SVG attributes\n */ const svgDefaults = {\n    \"xmlns\": \"http://www.w3.org/2000/svg\",\n    \"xmlnsXlink\": \"http://www.w3.org/1999/xlink\",\n    \"aria-hidden\": true,\n    \"role\": \"img\"\n};\n/**\n * Style modes\n */ const commonProps = {\n    display: \"inline-block\"\n};\nconst monotoneProps = {\n    backgroundColor: \"currentColor\"\n};\nconst coloredProps = {\n    backgroundColor: \"transparent\"\n};\n// Dynamically add common props to variables above\nconst propsToAdd = {\n    Image: \"var(--svg)\",\n    Repeat: \"no-repeat\",\n    Size: \"100% 100%\"\n};\nconst propsToAddTo = {\n    WebkitMask: monotoneProps,\n    mask: monotoneProps,\n    background: coloredProps\n};\nfor(const prefix in propsToAddTo){\n    const list = propsToAddTo[prefix];\n    for(const prop in propsToAdd){\n        list[prefix + prop] = propsToAdd[prop];\n    }\n}\n/**\n * Default values for customisations for inline icon\n */ const inlineDefaults = {\n    ...defaultExtendedIconCustomisations,\n    inline: true\n};\n/**\n * Fix size: add 'px' to numbers\n */ function fixSize(value) {\n    return value + (value.match(/^[-0-9.]+$/) ? \"px\" : \"\");\n}\n/**\n * Render icon\n */ const render = (// Icon must be validated before calling this function\nicon, // Partial properties\nprops, // Icon name\nname)=>{\n    // Get default properties\n    const defaultProps = props.inline ? inlineDefaults : defaultExtendedIconCustomisations;\n    // Get all customisations\n    const customisations = mergeCustomisations(defaultProps, props);\n    // Check mode\n    const mode = props.mode || \"svg\";\n    // Create style\n    const style = {};\n    const customStyle = props.style || {};\n    // Create SVG component properties\n    const componentProps = {\n        ...mode === \"svg\" ? svgDefaults : {}\n    };\n    if (name) {\n        const iconName = stringToIcon(name, false, true);\n        if (iconName) {\n            const classNames = [\n                \"iconify\"\n            ];\n            const props = [\n                \"provider\",\n                \"prefix\"\n            ];\n            for (const prop of props){\n                if (iconName[prop]) {\n                    classNames.push(\"iconify--\" + iconName[prop]);\n                }\n            }\n            componentProps.className = classNames.join(\" \");\n        }\n    }\n    // Get element properties\n    for(let key in props){\n        const value = props[key];\n        if (value === void 0) {\n            continue;\n        }\n        switch(key){\n            // Properties to ignore\n            case \"icon\":\n            case \"style\":\n            case \"children\":\n            case \"onLoad\":\n            case \"mode\":\n            case \"ssr\":\n                break;\n            // Forward ref\n            case \"_ref\":\n                componentProps.ref = value;\n                break;\n            // Merge class names\n            case \"className\":\n                componentProps[key] = (componentProps[key] ? componentProps[key] + \" \" : \"\") + value;\n                break;\n            // Boolean attributes\n            case \"inline\":\n            case \"hFlip\":\n            case \"vFlip\":\n                customisations[key] = value === true || value === \"true\" || value === 1;\n                break;\n            // Flip as string: 'horizontal,vertical'\n            case \"flip\":\n                if (typeof value === \"string\") {\n                    flipFromString(customisations, value);\n                }\n                break;\n            // Color: copy to style\n            case \"color\":\n                style.color = value;\n                break;\n            // Rotation as string\n            case \"rotate\":\n                if (typeof value === \"string\") {\n                    customisations[key] = rotateFromString(value);\n                } else if (typeof value === \"number\") {\n                    customisations[key] = value;\n                }\n                break;\n            // Remove aria-hidden\n            case \"ariaHidden\":\n            case \"aria-hidden\":\n                if (value !== true && value !== \"true\") {\n                    delete componentProps[\"aria-hidden\"];\n                }\n                break;\n            // Copy missing property if it does not exist in customisations\n            default:\n                if (defaultProps[key] === void 0) {\n                    componentProps[key] = value;\n                }\n        }\n    }\n    // Generate icon\n    const item = iconToSVG(icon, customisations);\n    const renderAttribs = item.attributes;\n    // Inline display\n    if (customisations.inline) {\n        style.verticalAlign = \"-0.125em\";\n    }\n    if (mode === \"svg\") {\n        // Add style\n        componentProps.style = {\n            ...style,\n            ...customStyle\n        };\n        // Add icon stuff\n        Object.assign(componentProps, renderAttribs);\n        // Counter for ids based on \"id\" property to render icons consistently on server and client\n        let localCounter = 0;\n        let id = props.id;\n        if (typeof id === \"string\") {\n            // Convert '-' to '_' to avoid errors in animations\n            id = id.replace(/-/g, \"_\");\n        }\n        // Add icon stuff\n        componentProps.dangerouslySetInnerHTML = {\n            __html: cleanUpInnerHTML(replaceIDs(item.body, id ? ()=>id + \"ID\" + localCounter++ : \"iconifyReact\"))\n        };\n        return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(\"svg\", componentProps);\n    }\n    // Render <span> with style\n    const { body, width, height } = icon;\n    const useMask = mode === \"mask\" || (mode === \"bg\" ? false : body.indexOf(\"currentColor\") !== -1);\n    // Generate SVG\n    const html = iconToHTML(body, {\n        ...renderAttribs,\n        width: width + \"\",\n        height: height + \"\"\n    });\n    // Generate style\n    componentProps.style = {\n        ...style,\n        \"--svg\": svgToURL(html),\n        \"width\": fixSize(renderAttribs.width),\n        \"height\": fixSize(renderAttribs.height),\n        ...commonProps,\n        ...useMask ? monotoneProps : coloredProps,\n        ...customStyle\n    };\n    return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(\"span\", componentProps);\n};\n/**\n * Enable cache\n *\n * @deprecated No longer used\n */ function enableCache(storage) {\n//\n}\n/**\n * Disable cache\n *\n * @deprecated No longer used\n */ function disableCache(storage) {\n//\n}\n/**\n * Initialise stuff\n */ // Enable short names\nallowSimpleNames(true);\n// Set API module\nsetAPIModule(\"\", fetchAPIModule);\n/**\n * Browser stuff\n */ if (typeof document !== \"undefined\" && \"undefined\" !== \"undefined\") {}\nfunction IconComponent(props) {\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(!!props.ssr);\n    const [abort, setAbort] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({});\n    // Get initial state\n    function getInitialState(mounted) {\n        if (mounted) {\n            const name = props.icon;\n            if (typeof name === \"object\") {\n                // Icon as object\n                return {\n                    name: \"\",\n                    data: name\n                };\n            }\n            const data = getIconData(name);\n            if (data) {\n                return {\n                    name,\n                    data\n                };\n            }\n        }\n        return {\n            name: \"\"\n        };\n    }\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(getInitialState(!!props.ssr));\n    // Cancel loading\n    function cleanup() {\n        const callback = abort.callback;\n        if (callback) {\n            callback();\n            setAbort({});\n        }\n    }\n    // Change state if it is different\n    function changeState(newState) {\n        if (JSON.stringify(state) !== JSON.stringify(newState)) {\n            cleanup();\n            setState(newState);\n            return true;\n        }\n    }\n    // Update state\n    function updateState() {\n        var _a;\n        const name = props.icon;\n        if (typeof name === \"object\") {\n            // Icon as object\n            changeState({\n                name: \"\",\n                data: name\n            });\n            return;\n        }\n        // New icon or got icon data\n        const data = getIconData(name);\n        if (changeState({\n            name,\n            data\n        })) {\n            if (data === undefined) {\n                // Load icon, update state when done\n                const callback = loadIcons([\n                    name\n                ], updateState);\n                setAbort({\n                    callback\n                });\n            } else if (data) {\n                // Icon data is available: trigger onLoad callback if present\n                (_a = props.onLoad) === null || _a === void 0 ? void 0 : _a.call(props, name);\n            }\n        }\n    }\n    // Mounted state, cleanup for loader\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        setMounted(true);\n        return cleanup;\n    }, []);\n    // Icon changed or component mounted\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (mounted) {\n            updateState();\n        }\n    }, [\n        props.icon,\n        mounted\n    ]);\n    // Render icon\n    const { name, data } = state;\n    if (!data) {\n        return props.children ? props.children : props.fallback ? props.fallback : /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(\"span\", {});\n    }\n    return render({\n        ...defaultIconProps,\n        ...data\n    }, props, name);\n}\n/**\n * Block icon\n *\n * @param props - Component properties\n */ const Icon = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)((props, ref)=>IconComponent({\n        ...props,\n        _ref: ref\n    }));\n/**\n * Inline icon (has negative verticalAlign that makes it behave like icon font)\n *\n * @param props - Component properties\n */ const InlineIcon = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)((props, ref)=>IconComponent({\n        inline: true,\n        ...props,\n        _ref: ref\n    }));\n/**\n * Internal API\n */ const _api = {\n    getAPIConfig,\n    setAPIModule,\n    sendAPIQuery,\n    setFetch,\n    getFetch,\n    listAPIProviders\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@iconify/react/dist/iconify.js\n");

/***/ })

};
;